{"version": 2, "files": [{"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.2.3/lib/src/method_channel/utils/codec.dart", "hash": "020552519d966b598cd3bb17849a3a49"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/changeNotifiers/NudronChangeNotifiers.dart", "hash": "cd6193ac268d9ce17e87d6f8b77abaa7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/pages/BackgroundChart.dart", "hash": "141d86590094764475825283cdc41eb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/LICENSE", "hash": "5d89b1f468a243c2269dfaceb3d69801"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "1b3814e3cd3f2d9543c7ebaf88384e10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_13_pro_max/device.dart", "hash": "a229010417afb8eda88d39448e75a43d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/lazy.dart", "hash": "66b974c03e0f08d662d0bdfd2edb44f5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/togglebg.png", "hash": "eee052a3ca9486f60447efcda0cdd227"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationexpandcollapsepattern.dart", "hash": "4a909f493f4dd8dfb93d3a3d4843bd77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ishellitem.dart", "hash": "2ea28d523e25da87fbda7e73bc2ffedf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/checked_helpers.dart", "hash": "c668a1bfe65f14c115a3294ac6502dca"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ma.png", "hash": "c0a2741a2923ecf89d6816db5973ae92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/stacked_area100_series.dart", "hash": "82e4e37ec6462208e26d42b0415a7f3e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "d7c9baf97f1348c00c56f8d64a3ce53a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "b815d11a718e0a4d6dec5341e2af4c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/axis/datetime_axis.dart", "hash": "05d08aebb46ce7218490d4f58f4242c0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/et.png", "hash": "2c5eec0cda6655b5228fe0e9db763a8e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "511ff5c6f0e454b22943906697db172f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/hk.png", "hash": "51df04cf3db3aefd1778761c25a697dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/io_client.dart", "hash": "e4823f5eb1dffcf1cf47a9d667c5cb18"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "b3465d5b02dd4743d8d9f9e4170a1151"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/utils/toggle_button.dart", "hash": "b1ecdf2846cfee4959a6e64bc0dea4cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "hash": "9225936ad56490ca8492f0ed59a816d0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cd.png", "hash": "55a65054ab0bcddd4fd0af90e5ed008a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/es_messages.dart", "hash": "2c560be43d64cf41fc2a6a5da173149b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ipersist.dart", "hash": "98911449216f1b1c1b092954bd6cebc5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/jp.png", "hash": "b7a724413be9c2b001112c665d764385"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/utils/sequential.dart", "hash": "b5519514c9b9570c951c0da186030e29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/flatten.dart", "hash": "32555fa5e63954e6fe4b4f07817914eb"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "16903e1f0bc6b66d30a5804b7ae71fe5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/na.png", "hash": "3499146c4205c019196f8a0f7a7aa156"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/functions.dart", "hash": "a12fc767bd933ecc3bbdd69f597ed3cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/predicate/character.dart", "hash": "c1d88c6f9a0dbed4be35c285ffac4da6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispeechobjecttoken.dart", "hash": "47cee6326ea5f9f09e1247e2930199e2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mg.png", "hash": "a562a819338427e57c57744bb92b1ef1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/images/basic_advance.png", "hash": "0f600be57e17529f4887fc79100a4ef8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/storage/preferences/preferences_io.dart", "hash": "433d1e7e1281abbee4a2394d97134a11"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/doctype.dart", "hash": "a0ff9321b483226cdbe4773e33779715"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "4372cb3b63b820aff3fe67061bba3f9f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "5cedacfe2fd447a541cd599bfc1aef91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/callbacks.dart", "hash": "86781b32fca02e40f75c1b196e60fe4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/widgets/draw_ring.dart", "hash": "49805b7c6cbee18695c78cb324f3d0b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationelement5.dart", "hash": "e053a966b20fda12dc7d24e0f56c845a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mo.png", "hash": "da3700f98c1fe1739505297d1efb9e12"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/uy.png", "hash": "20c63ac48df3e394fa242d430276a988"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ck.png", "hash": "35c6c878d96485422e28461bb46e7d9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib/path_parsing.dart", "hash": "498f254119e3d3c67475fe8ca026d01a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "94235ba74c3f3ad26e22c4b40538ce07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/screenutil_mixin.dart", "hash": "4a394a54b8e7be1d7c9df7f10c89cd05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/bstr.dart", "hash": "af04c2a11aa95d2e4b86600b33d0957c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/kn.png", "hash": "5921868fc0cee7ee1779e8d0da3aa2a0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ao.png", "hash": "d19240c02a02e59c3c1ec0959f877f2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/paint.dart", "hash": "dfbfb22ff7f0f68f8c6b4a1e84303d8f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "3405e08e614528c3c17afc561d056964"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/utils/renderer_helper.dart", "hash": "57b7f5e8429a5356bd58b2631d1e6735"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/LICENSE", "hash": "400e734be24f6a778abd1077d5bd6a2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/base.dart", "hash": "b218cc2e8a96a232e8c55f53d1780381"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/util/utils.dart", "hash": "01fb027f088c7e4d266f7f4c195bc2b2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "5979a1b66500c09f65550fab874ee847"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "3303320b233b1ca33a9e6e8c93e2d2c9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "59475498db21e2333db54d6478af7c94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/enum_helpers.dart", "hash": "32c8f2d4dc53cfe56f5fa637be2c52e7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sn.png", "hash": "25201e1833a1b642c66c52a09b43f71e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.15/lib/src/render_object_selection.dart", "hash": "dd006282a4ae0bc84d10206ea7441925"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "c2e0fa3415ed461288b6e2aecf569919"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "d3abf203392ec29c7ebbda6b41360d2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib/src/bloc_base.dart", "hash": "8677c75c2578c594ba5a90bc91c73571"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "36fc598c656490ab430ca1be5fb909e8"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "ba2f8adc4e6c096b09aac919580fffee"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/az.png", "hash": "967d8ee83bfe2f84234525feab9d1d4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/dots_triangle/build_sides.dart", "hash": "20eb9998e128202969bc06d36f4f86dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parent_exception.dart", "hash": "2ede71f09a240decbc57417850f8feb7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_breadcrumb-1.0.1/lib/src/breadcrumb_overflow.dart", "hash": "ffca0108c16594121210d1f59d34aa45"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/pwd.svg", "hash": "166e2b3a0f1294339990a9efbe3599be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/constants.dart", "hash": "aa402e044e82ab02e7161a2be4697135"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/pubspec.yaml", "hash": "bde10289727798574562861b39826ffe"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/bloc/dashboardBloc/dashboardEvent.dart", "hash": "b07821beea9977bec7883d70efd0d54a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ug.png", "hash": "dd8c47c7243ac213a236c3abdfd416c4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/_web_image_info_io.dart", "hash": "e4da90bb20b3980a03665a080c87a098"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "0a546a51fffe9612c8c3cbebc609691c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_13_mini/device.dart", "hash": "54c77567e208adcb18adbfbdd7e43146"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/utils/misc_functions.dart", "hash": "8ec360758eda0424504e57f854132988"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/about.dart", "hash": "4bf9cb0fbb8b0236f0f9e554c7207a4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ie.png", "hash": "5790c74e53070646cd8a06eec43e25d6"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cx.png", "hash": "65421207e2eb319ba84617290bf24082"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/pick.dart", "hash": "c60b204fb5e7d501c0addb330c88d2de"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "a32174b6de983c1652638940e75aae6a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/uy.png", "hash": "20c63ac48df3e394fa242d430276a988"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/tool_panel/sections/settings.dart", "hash": "9bbc900e4604b95a30a1ec224442b028"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/processing.dart", "hash": "5a7bd956aa537e95be882d4809232c39"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "1603f38e802a78686ee48e3554da22f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/lib/open_file_ios.dart", "hash": "838b57aaab0b5dda6a3961930fb53c45"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "2d4b5a2778f275040b5e438045607332"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/screen_util.dart", "hash": "b42f45e5b9cc360364d8edb9cfefe2e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix3.dart", "hash": "64b9fc5ffdc9f1ba801b6ccf099347b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ec.png", "hash": "cbaf1d60bbcde904a669030e1c883f3e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/kn.png", "hash": "5921868fc0cee7ee1779e8d0da3aa2a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/tool_panel/widgets/search_field.dart", "hash": "24599fe696c74d94cbb47b9eb36a783a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/http.dart", "hash": "ddbd4db4cc029d33f28b41110e89d419"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/api_ms_win_shcore_scaling_l1_1_1.g.dart", "hash": "1c0e59efab3b5ae568836b9fa5a3675d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/a5.svg", "hash": "934762f95d197e371e6eaa6d9f3a19aa"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "1c7764fa08241a44711301c74fb658df"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/a0.svg", "hash": "eb0c0eb2895d3e0c1216a8a5f4faf648"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "72804f9d34b9a247c43d6cc575527370"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/definition/internal/undefined.dart", "hash": "bb00c98e50d3c71d4ab7ac7c46122f3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/vc.png", "hash": "a604d5acd8c7be6a2bbaa1759ac2949d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudioclock.dart", "hash": "7c32424ef2aaa2f268fe177af2d4731f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cu.png", "hash": "8d4a05799ef3d6bbe07b241dd4398114"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vibration_platform_interface-0.0.2/lib/vibration_platform_interface.dart", "hash": "6b0b53595f5fbecb8ee51a236b39e156"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iunknown.dart", "hash": "4c90e2a275589188bd8714dd9cc5650a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/samsung_galaxy_note20_ultra/screen.g.dart", "hash": "3a997c8a5fa9cec65323930ccbfd2815"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/widget/_pin_state.dart", "hash": "081a5cbbe9477a6843b9df0a95f5f5e1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "b3f8f8ba0560319908ddb5d9480a5788"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/string.dart", "hash": "1aaa0309ba77b0f57733e99543c455ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/legacy_api.dart", "hash": "e62a8f39ad332b5e313b0be97f2d280f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/id_messages.dart", "hash": "6e5169b25eda9d7eb9509b58a0bdc51f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ishellitem2.dart", "hash": "b0c96b9383b0471bcadb2206daedef05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/state/state.freezed.dart", "hash": "9ff2925e03d534cc3b59dbe573824b81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/xinput1_4.g.dart", "hash": "110291e1a5dee5de6d06425145c9f53c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/pattern/pattern_iterator.dart", "hash": "accb24637ddbe55d7a3f76e4618bdd22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_t.dart", "hash": "8f142b64056bff3425661bf170728f45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/scatter_series.dart", "hash": "95fb313dd7abcc3f7854c5869e53b3cd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "6e320dd3d12f0e125541bc4b983dcfa7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pedantic-1.11.1/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "e01f6851d87ad96cbdafcbfd282517e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/pg.png", "hash": "06961c2b216061b0e40cb4221abc2bff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "78ce7527fa364df47ba0e611f4531c2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/node_codec.dart", "hash": "1de9311ba0f47dfc96166daab936f705"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/immnotificationclient.dart", "hash": "1cf0553fea22eee05a0cbb29e299760a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bd.png", "hash": "5fbfa1a996e6da8ad4c5f09efc904798"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "7ebcf3ce26dea573af17627d822e9759"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "b56cf23d49289ed9b2579fdc74f99c98"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "92137effa05660558f35cfc5845783bc"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "dd518cb667f5a97b3456d53571512bba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/conversion_sink.dart", "hash": "efcbc6fd4212ea81281561abddbf29f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/rometadata.g.dart", "hash": "cad4664b0591b1060e3eb77fc3cfdfd9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "61d3c1705094ee0ea6c465e47b457198"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/theme.dart", "hash": "52b05947a1dcb617334912d79888c6b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/funnel_chart.dart", "hash": "37fd90f5b1d4576c4c4741ec9be85f7d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "1bdb47a9af4b0a5d759937da8ff04db0"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "3f2a39352a1c6067566f8119aa021772"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ne.png", "hash": "a152defcfb049fa960c29098c08e3cd3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/matches/matches_iterator.dart", "hash": "4c92351d347c52a00797317aa487600f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ivirtualdesktopmanager.dart", "hash": "83c5918696d44ca1be713318a4f5a6db"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/s7.svg", "hash": "c2aab48e02ccc7028aafb0c31e432ba5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.3.0/LICENSE", "hash": "c17706815151969aa7de6328178cc8bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/axis/multi_level_labels.dart", "hash": "f84436e0b167e39c36ff05ed7840fe25"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "42c4c0281ec179aea5687dbced56aca7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/multipart_file.dart", "hash": "0a31dcaeb18fc2ec730e799a0bdd0397"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ienumidlist.dart", "hash": "043bb1fa01132048a01458c6977636f5"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "4da5ad5941f2d5b6b3fbb3f7ea217b41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.1/lib/src/connectivity_plus_linux.dart", "hash": "2aea038844961a04f31f81fbd8503cb2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/a7.svg", "hash": "d6a14672566f812c3bf72589ac471a7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "a02235e1a98989d6740067da46b4f73d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationselectionpattern2.dart", "hash": "2783f528d559449fbd0b97561717c83d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/processing.dart", "hash": "0ca8410c364e97f0bd676f3c7c3c9e32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/lib/src/span_scanner.dart", "hash": "87bcefcfff19652ad296ec7005799840"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/in.png", "hash": "c2e7c34b638189eb9b04774076e02e82"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/rendering.dart", "hash": "4bd3950a0bf4a9f9b09f97594e363d36"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/activity.svg", "hash": "4412169e734a95d2f50e22632dd397b1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/lb.png", "hash": "b21c8d6f5dd33761983c073f217a0c4f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "991024814d51967a20be5851be93a8e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "9298606a388e3adb5f1bbe88ae45b1e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/calendar/calendar_helper.dart", "hash": "98ddc31151afa5dd136193132a2e1eb9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.46/lib/local_auth_android.dart", "hash": "d1014fc9427deb3355db0b126083ed2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/comdlg32.g.dart", "hash": "9821568488904c8c1566c46762278f16"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/kw.png", "hash": "b2afbb748e0b7c0b0c22f53e11e7dd55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.1/lib/src/shared_preferences_legacy.dart", "hash": "4144d8b8e1cae585ab9f01406b3e1f75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/shared_preferences_linux.dart", "hash": "492280af61b4bca29e21d28db0c2be1c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/st.png", "hash": "7a28a4f0333bf4fb4f34b68e65c04637"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ifilesavedialog.dart", "hash": "a629548f10bfeaa42dfecec77c11b6f7"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "1338341fe43eb21f20857cc392cf2f71"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/eu.png", "hash": "b32e3d089331f019b61399a1df5a763a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/utils.dart", "hash": "b3c645b67738d6348499b11034a6a801"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/mn_messages.dart", "hash": "ccce344b2e8810f31cd17f9c92bd831e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/fa_messages.dart", "hash": "69ed9f3504f9dbb2f884d6941815b85f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "1f131d7f971396d52ce5fe78ae6a8a83"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cr.png", "hash": "475b2d72352df176b722da898490afa2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/hr.png", "hash": "04cfd167b9564faae3b2dd3ef13a0794"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/not.dart", "hash": "5bda4c1f149d153642bd503e97906b08"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ve.png", "hash": "f5dabf05e3a70b4eeffa5dad32d10a67"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "93d025adfc0409629c51036cb0fdc085"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/web_browser_info.dart", "hash": "9e887cddbdf6c6c7c650a995832b127f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/oneplus_8_pro/frame.g.dart", "hash": "95a7330fb74217078cbb9ba939e0c4aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/indicators/wma_indicator.dart", "hash": "eb61fda1bcc4ebd46928865c8e6c2bb7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/dz.png", "hash": "93afdc9291f99de3dd88b29be3873a20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/utils/failure_joiner.dart", "hash": "322037160bbd0d8f16bd4e77abfc61f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/api_ms_win_core_winrt_error_l1_1_0.g.dart", "hash": "0c553b8a000e02d64689984657b137a6"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/il.png", "hash": "b72b572cc199bf03eba1c008cd00d3cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_windows-0.0.3/lib/open_file_windows.dart", "hash": "760a1420ff09dbedeac7fe4f560ed281"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/lib/src/relative_span_scanner.dart", "hash": "b9c13cdd078c3b28c3392f0d6d5d647b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.1/lib/connectivity_plus.dart", "hash": "9b43d6f9384a837bbd0d8474e2365c7a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gt.png", "hash": "df7a020c2f611bdcb3fa8cd2f581b12f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ma.png", "hash": "c0a2741a2923ecf89d6816db5973ae92"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "5aa32c5e6b696b66556b4f91bf5983a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationselectionpattern.dart", "hash": "89afb95565b4d1eca335d4b9b4790212"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/st.png", "hash": "7a28a4f0333bf4fb4f34b68e65c04637"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/optional.dart", "hash": "7d49b944ccc5ee228590126488731a95"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/button.dart", "hash": "d7a239f8b80f844857527c2012e4fa1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/oleaut32.g.dart", "hash": "fae27a92131d4f2f2005c5312e499b8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/allocation.dart", "hash": "7c8e196c6c96efaadb0e605ec3cb1cce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/parser.dart", "hash": "13255a7d5a3edaa79e467810f1290f48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/parsers.dart", "hash": "2faf7d9b6110bf3fef7655742f7bfc23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "7088cc45b21c93be6b42dc748fc3a29a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/kh.png", "hash": "cd50a67c3b8058585b19a915e3635107"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mv.png", "hash": "69843b1ad17352372e70588b9c37c7cc"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cd.png", "hash": "55a65054ab0bcddd4fd0af90e5ed008a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "e27d4685e9e6aa906547a77095cc1ac5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/do.png", "hash": "a05514a849c002b2a30f420070eb0bbb"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "f8275b74f8f83272b8a8d1a79d5b2253"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "883b210f4cc20daebdb2834dbe4a512c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ae.png", "hash": "045eddd7da0ef9fb3a7593d7d2262659"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/page.dart", "hash": "6b16a4d19243ba00762af7e39dafc177"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/pages/EnterTwoFacCode.dart", "hash": "363ba19eda591e6089e1b900b6f3f3a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/expression/result.dart", "hash": "bc503b6c5e3658a13efaee4e0638935a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ishellitemarray.dart", "hash": "40abc849ae2322b6e6a63d567f952f1d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/pf.png", "hash": "3ba7f48f96a7189f9511a7f77ea0a7a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "dc552952c58db02409090792aeebbdd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ifileisinuse.dart", "hash": "5abf40e886af8feb42ccc62d31044f48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/LICENSE", "hash": "906742df8afd59744edfde69b6b6f7e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_utils.dart", "hash": "bf850e483673d93e76e1fd5c69d8135a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/lib/src/eager_span_scanner.dart", "hash": "bdc22e9e77382045196b5aafd42b5e55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/trimming.dart", "hash": "1db476563b55e241003667ca3669c0b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.dart", "hash": "c6f78ebc1239a030ffc141df9b33aed1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "0fbec63144acf1cb9e5d3a3d462e244b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/token.dart", "hash": "8006c8d72d7de5fbf9f6034104c30166"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/pe.png", "hash": "724d3525f205dfc8705bb6e66dd5bdff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/imetadataassemblyimport.dart", "hash": "bcb3a959e03b0ba17fa42d5f919b2e00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_12_mini/screen.g.dart", "hash": "3c3ea5d208b7ed4fb118757b17447db6"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/mail.svg", "hash": "fefd20c8a85e5052ba3c4c3b02e04f1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/google_fonts.dart", "hash": "885d6001f197c05de34b17e76acc7ed4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "0bb85eff209a2008dc5f47b2beda5bf3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/falling_dot/falling_dot.dart", "hash": "d558076037d704d740b34039d4d568a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.3/lib/options/web_options.dart", "hash": "7dff3a0a1b5652f08f2267907c79844e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "0ae47d8943764c9c7d362c57d6227526"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sz.png", "hash": "5e45a755ac4b33df811f0fb76585270e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_12/screen.g.dart", "hash": "b8b2b8d71f3a4e617ef1a21aea8894c2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "24094ce9de1b9222a8d6548d3c01045a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sh.png", "hash": "fc5305efe4f16b63fb507606789cc916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_p.dart", "hash": "af493bb7ab298cddebf04d46f7c5dc18"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/profile.svg", "hash": "5bf9ecc6dadcb804e33db40e7f6b4b12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/marker.dart", "hash": "c59cad0f12f2c5d0c9f72d4d6ba0ca99"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/cupertino.dart", "hash": "9b83fabf1193bf4967b740dd7a2c8852"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/utilities/screenshot.dart", "hash": "b76f3a5bcac840b26df78268e8dab479"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/LICENSE", "hash": "1c52a06a48033bea782314ca692e09cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/shared_preferences_windows.dart", "hash": "2bc47cc0ce47761990162c3f08072016"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "505d7dde41bffe17b69e52db6ab37d0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/method_channel_shared_preferences.dart", "hash": "513d6195384503beeb7f3750e426f7bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/pyramid_series.dart", "hash": "b3bcd92f2662e5db5a8cf28a18a656ec"}, {"path": "/Users/<USER>/Development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/calendar/hijri_date_time.dart", "hash": "abbbd0ee8929dfeb6d52813c45e23008"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gn.png", "hash": "765a0434cb071ad4090a8ea06797a699"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sa.png", "hash": "ef836bd02f745af03aa0d01003942d44"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/zw.png", "hash": "d5c4fe9318ebc1a68e3445617215195f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ga.png", "hash": "fa05207326e695b552e0a885164ee5ac"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/eg.png", "hash": "9e371179452499f2ba7b3c5ff47bec69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/types.dart", "hash": "ce0d3155596e44df8dd0b376d8728971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/comment.dart", "hash": "87546066dfc566126ed9357805535e97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/LICENSE", "hash": "1eadf5be3116dc31e5f04d4d6d352497"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/sheet/cell_style.dart", "hash": "e507924fd6396a87be37b0f3141eaf1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/gr_messages.dart", "hash": "e2aa1620aa833035d1cea0e0556b7bad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationinvokepattern.dart", "hash": "1d7963ea64a6b7059dc1f694f23f0b98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_12_mini/frame.g.dart", "hash": "e0395d9e13dcce78cb5957bdfcdf58cb"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "6aad5f436704faf509d60ddb032f41b4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/scribe.dart", "hash": "d195153a8c01a0392b38e3b9adc672d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudioclient3.dart", "hash": "025a4f4e26446bf3da88ee7d3cf3d0f2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/utils/no_entries.dart", "hash": "c8abaaae914d0833f93c55b91a119d44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/bluetoothapis.g.dart", "hash": "eeeb5875589f6bf64f72c094f0435b92"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/utils/new_loader.dart", "hash": "5d3ef75cb92d9678bd49ea9988263e2b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mh.png", "hash": "2a7c77b8b1b4242c6aa8539babe127a7"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "2de077d432c4bb0a9525e9ab5d84913a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/parser.dart", "hash": "fe8bc1809af0e6213c525f120eeaa96b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/src/multi_bloc_provider.dart", "hash": "41cff63fc16905d44977472328ef814a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "dcb5ce635282f4390eca8dcb73737991"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/powrprof.g.dart", "hash": "2a4b4b7e76f1c79a762ce636f6b730db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "9d6f9dd391f828bccdbb47c5072c04c1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "81fd3ef494f4443fb8565c98ba5a9ba2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/dwmapi.g.dart", "hash": "607cef6a651964e0339d2e09df046c09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/chunked_coding/encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/utils/biometric_helper.dart", "hash": "cf61bdcb0d543118e42f440693ce544b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/utils/getDeviceID.dart", "hash": "ffdc39e255b795fc0f485c33826d07fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.2.3/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ba.png", "hash": "9faf88de03becfcd39b6231e79e51c2e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/pl.png", "hash": "a7b46e3dcd5571d40c3fa8b62b1f334a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "17a28a030318e2c8f8fd653e0b862d50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/src/listenable_provider.dart", "hash": "fe16b487322631b50c3cbb09de987315"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.3/lib/options/android_options.dart", "hash": "2d04b343ac3e272959ffa40b7b9d782c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/types/auth_messages_windows.dart", "hash": "c88d29163ef68ff67276b64f3a1331cd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "9c13d1f810b039faf38c54f062c83747"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/imetadatatables.dart", "hash": "fbce92f0e78e457538005bcb0b9a79f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/utils/separated_list.dart", "hash": "031cc72717282ff9f7f588e73fb0fd0b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "3ee6304161ca2993b303a8074557fe66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_peer.dart", "hash": "681b70272ec68e757f2394c9e7fa9398"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "43ef2382f5e86c859817da872279301e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "bb020f793a10d8bb46c0bbc996bd0bfb"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "f7b9c7a2d1589badb0b796029090d0d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/crypt32.g.dart", "hash": "6848c6ac5c6c2b1b40f3dd8ec0bbe31c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/namespace.dart", "hash": "d7259aeee1602df30d051e8fc0523d91"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "019f7b771f1865632d5a36c9e74296db"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "c98d71a32518e80bc7cf24b1da6c9c57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/step_area_series.dart", "hash": "5bf48a703ac35f8f112cf2725a98b2fc"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "74a89d22aa9211b486963d7cae895aab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_parent.dart", "hash": "a7ac3293430577fa9c028b0df6607fa4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/license.dart", "hash": "a7afa9732f8f4a0bcf1ca2012f613943"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/two_rotating_arc/two_rotating_arc.dart", "hash": "42a94d6a17467ee88a3ee6b5561fe8ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.3/lib/options/linux_options.dart", "hash": "26c4f0c369b83e53900ac87bf7e0dcff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/bidi_formatter.dart", "hash": "5c81dd07124ccc849c310595d9cfe5be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "9802442b82d3be84abecae8d0a2c7bd6"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/discrete_circle/discrete_circle.dart", "hash": "a23635d68d7635b17199bdbc1b2394c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/normalizer.dart", "hash": "b6fde0bb78218226247a2173dbf96ea5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "3d27bed38f1893769396b5d23f94f15e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/imetadataimport.dart", "hash": "b8252455a884dfc13966cec360c9844d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/parser/parse.dart", "hash": "25a3106b3ba21cc573dfb4fc1e7513d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iconnectionpoint.dart", "hash": "ed361e60fcf89da03b59c13d84579d0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vibration-2.0.1/LICENSE", "hash": "6acb14e9d619a7b8e8d3b7ea12274470"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_object_manager.dart", "hash": "5f173a5c0de15909e95d3275051138c1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sh.png", "hash": "fc5305efe4f16b63fb507606789cc916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_13/frame.g.dart", "hash": "35924279cb0b62003988a8fb359f6857"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ve.png", "hash": "f5dabf05e3a70b4eeffa5dad32d10a67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/pretty_writer.dart", "hash": "4c618cb90a20b93f23c554b8745d5f77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtreewalker.dart", "hash": "865471d167a94c3a9bad6cea64f10834"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib/src/change.dart", "hash": "48ded007bb6906ea52b717909ab19ec3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/lc.png", "hash": "055c35de209c63b67707c5297ac5079a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "e892b3496135877dd5a0ea2ea2fc91e8"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bl.png", "hash": "30f55fe505cb4f3ddc09a890d4073ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/en_messages.dart", "hash": "5be1b84d7225a777b716740a573005e2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/rw.png", "hash": "6ef05d29d0cded56482b1ad17f49e186"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/la.png", "hash": "8c88d02c3824eea33af66723d41bb144"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_parent.dart", "hash": "7f47dda6ed10e33236d465680dc8c12b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/util/radius_util.dart", "hash": "d5c2961938ba31b62fd4fbeb0344dcc3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "hash": "594a02a84320cea18e06458c446d98e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/info/identifier.dart", "hash": "a3d868fed0246232a9c76aa985d2f96c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/trends.svg", "hash": "3febc86f0efbd5680701d5e8d263b382"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "900a13c9fcd73f4e8e3d069d76af6ffa"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/at.png", "hash": "7edbeb0f5facb47054a894a5deb4533c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationgriditempattern.dart", "hash": "f558b0876d2ee3eb7fc5b350a5ef85e7"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "e05529d31a09e4c86cde70483824fa10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/loading_animation_widget.dart", "hash": "20bb8bcbc900d30f5d4241f7d2e168fb"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/lib/src/country_codes.dart", "hash": "34c383669af3062a01c81a41397cabfc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.17/LICENSE", "hash": "a02789da8b51e7b039db4810ec3a7d03"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "44927d8a4e3825e7c3be0af91307d083"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mw.png", "hash": "efc0c58b76be4bf1c3efda589b838132"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/dv_messages.dart", "hash": "83608e8582ac2172c5d25970c1d5bfe8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/src/messages.g.dart", "hash": "814815839a4b6d2924a5a8661780b0cc"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/dk.png", "hash": "f9d6bcded318f5910b8bc49962730afa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/builder/property_builder.dart", "hash": "3c11d3424aa1cbecfc8de9834d8382ac"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/jm.png", "hash": "3537217c5eeb048198415d398e0fa19e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gb-wls.png", "hash": "72005cb7be41ac749368a50a9d9f29ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad/frame.g.dart", "hash": "3834d3fd45f4dd928310fef4b34963b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/nb_no_messages.dart", "hash": "c25db1d18f943e8c111a995433ddfbd5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_introspectable.dart", "hash": "a8d03ee07caa5c7bca8609694786bbf0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/az_messages.dart", "hash": "383449705ab4829f1ac6f743d1f9b948"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "64a2ea17e8058aec30096102af030f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/LICENSE", "hash": "0dc6cec7b4ef07b48069a9ff484a8ffe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/regexp.dart", "hash": "10ca1bc893fd799f18a91afb7640ec26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "hash": "5d89b1f468a243c2269dfaceb3d69801"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.15/lib/vector_graphics_compat.dart", "hash": "a017616228e06275ed5610a4368f8d84"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/lv.png", "hash": "6a86b0357df4c815f1dc21e0628aeb5f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ar.png", "hash": "bd71b7609d743ab9ecfb600e10ac7070"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml.dart", "hash": "2c4c36a5cc838977cf822b6db5d9200a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "49194534260502aa020910c20fb3ad6a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "a46ede2164234d7371852e8f57865dd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/am_messages.dart", "hash": "53e91cefa593528a982f5ec0f6984a5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/gauges_theme.dart", "hash": "31c96cb853eef916a8d0c020d525df65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/_initialize_path_ops_io.dart", "hash": "518899270d23bb8654ecbc26f12702a1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bg.png", "hash": "bb279455eec0de36199f1453c1c67042"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/draw_command_builder.dart", "hash": "f1457454215dff1ed947363fcbf4aeba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/api_ms_win_core_winrt_string_l1_1_0.g.dart", "hash": "bb40d6ae10f99afb1b498e44b75f9a3b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/extensions/set_string.dart", "hash": "097e09840cc00325fdbebaacd05f4827"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.1/lib/src/shared_preferences_devtools_extension_data.dart", "hash": "3f47c1f73c7a4541f98163b83d056456"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/legend.dart", "hash": "acfd6c2d70e299e43728b9539a2aeb58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/src/flutter_secure_storage_windows_ffi.dart", "hash": "2ca4b9e92c39403717d2dcc32bed57e3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/hu.png", "hash": "46b4e675795ba7d526187e1f97841fca"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "93c17b2980fc5498f3ba266f24c6b93b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/images/basic_advance2.png", "hash": "be17da8c0ad0fbfa77620361279ca89b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vibration_platform_interface-0.0.2/lib/src/method_channel_vibration.dart", "hash": "059c10f54f03a06686030fe5de3b0841"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "3cb88cf9e4198e0d510b78aa005aa597"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/pe.png", "hash": "724d3525f205dfc8705bb6e66dd5bdff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/named.dart", "hash": "c5f3b8d4c2e6f53c5fcbdde1e0f03f4b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "e324dd19cc02a1bf47bf7cc545dcca79"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "9f05403438068337dd8f3433d2757535"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cm.png", "hash": "89f02c01702cb245938f3d62db24f75d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/number_parser_base.dart", "hash": "39348131fc86fb08a42dd6b2d1b16bf0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/generic/desktop_monitor/device.dart", "hash": "243cbaabc01f25abe6e250bca0196bb5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_key.dart", "hash": "6a18f9347e6e639ebbbfb0a0ce98bb71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_foundation.dart", "hash": "db8ef5ac4d806e72f7b356056cb50b1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/indicators/roc_indicator.dart", "hash": "bf0974afb6ad7fda6ebc91779182d7e5"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "04f538d5fc784c89c867253889767be4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/inetworklistmanager.dart", "hash": "e165be390861acd35be3189fe414b105"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/predicate/string.dart", "hash": "c59198554d2a2402363b7e7671fded95"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/theme_widget.dart", "hash": "f270bac871f987101886a52a9e330632"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/jm.png", "hash": "3537217c5eeb048198415d398e0fa19e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "44c1268c1ecafd3b4cd06ab573f6779a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/spark_charts_theme.dart", "hash": "b6214710c7e03c887ee16c9e84979305"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mh.png", "hash": "2a7c77b8b1b4242c6aa8539babe127a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib_decoder.dart", "hash": "f579367d110cce54117aec036fdb4074"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/do.png", "hash": "a05514a849c002b2a30f420070eb0bbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/it.png", "hash": "99f67d3c919c7338627d922f552c8794"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/following.dart", "hash": "662638321f1933cdab78277f222b8aa5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "9a977b88944bf59512e9d8aaeef93605"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/date_format.dart", "hash": "20dc50b53035a8e953b5d4ffe6948ede"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "1ae1a412c9f9daff34b9dd63e60cec2d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/nf.png", "hash": "5e12fe669c82c295812e27e9f1e410c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/event_attribute.dart", "hash": "304fc982848b57cf13da0ec511f05ed9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/wlanapi.g.dart", "hash": "29247603a535c298681d43412512fd53"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/al.png", "hash": "af06d6e1028d16ec472d94e9bf04d593"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_method_response.dart", "hash": "f29d1458f73f015dabefc27f98181f05"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/vg.png", "hash": "0f19ce4f3c92b0917902cb316be492ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/list_converter.dart", "hash": "5f5f3a1074f40b8fc37c2b3ba5ec0432"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gq.png", "hash": "0dc3ca0cda7dfca81244e1571a4c466c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/circular_data_label.dart", "hash": "03c4dfa186c437d95bc2791d6b1facd3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/utils/smallbutton.dart", "hash": "5b1232e61211a35b794d69677741a257"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gn.png", "hash": "765a0434cb071ad4090a8ea06797a699"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sg.png", "hash": "94ea82acf1aa0ea96f58c6b0cd1ed452"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationvirtualizeditempattern.dart", "hash": "9583f92189dde339b1884f57e7b2f9b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/li.png", "hash": "c3bf7d5c792357ad1eb6e7cede352c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/ink_drop/ink_drop.dart", "hash": "1bd621f99e8c049da2be17386b7a6da9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.17/lib/src/utilities/_file_io.dart", "hash": "76964a546c84af33fb4bd8b2ba2fefda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_auth_server.dart", "hash": "0b4a237293e913152ca376cdcfbe752a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/method_channel_flutter_secure_storage.dart", "hash": "20e7221c12677486628b48b0c30569f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/LICENSE", "hash": "aca2926dd73b3e20037d949c2c374da2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/quaternion.dart", "hash": "82a52b42ca10c86b0f48afea0cbe9ac7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtogglepattern.dart", "hash": "b3d8ffb1e79fe86169ef197e01c7c79c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/large.dart", "hash": "e58e52daaaac27b271e244edbd1e6a37"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/billing.svg", "hash": "b3ff1525b8f077f37b8c55939eeefaf4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_8.dart", "hash": "9dbdc6f759d68f1ba1b47b561de1e299"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/aq.png", "hash": "c57c903b39fe5e2ba1e01bc3d330296c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cg.png", "hash": "1f1b349f054a4183e80149268c7c4d31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/separated.dart", "hash": "641f0dfad31a545ac6fa5515e83920fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_mac-1.0.3/lib/open_file_mac.dart", "hash": "2268cd6dbb365acb6559db057fd01cea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix2.dart", "hash": "ac51c125ed5881de5309794becbacc8b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/nudronlogo.png", "hash": "2f1fdb08569aeb7f67661a9564a8850c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/simple_name.dart", "hash": "208d1ef7a6cc2445551b3138139613bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_async_foundation.dart", "hash": "282aeeb78f4a92064354b5fe98161484"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/separated_by.dart", "hash": "04b800051d2d913cafc3733ee1bb21c1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "4d673eddc0bd2289539b66a92faae868"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sr.png", "hash": "e5719b1a8ded4e5230f6bac3efc74a90"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/vg.png", "hash": "0f19ce4f3c92b0917902cb316be492ba"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "5de15d7a41897996ef485c087ef4245b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_value.dart", "hash": "21beb4ff2c06d1edc806270e0bfac51f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationcacherequest.dart", "hash": "bec9a4fa9a224f42d622cf676a494a2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/default_mapping.dart", "hash": "72bbe921b18b48d52eb45666e3c52729"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mp.png", "hash": "60b14b06d1ce23761767b73d54ef613a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "7bd8137185bc07516a1869d2065efe0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.2/lib/src/messages.g.dart", "hash": "cf9319ed663313803cdd367976bbb8df"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/jp.png", "hash": "b7a724413be9c2b001112c665d764385"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.3/lib/test/test_flutter_secure_storage_platform.dart", "hash": "362bf1b65ae84f1129622a8814a50aad"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "hash": "42e6566e2397f48094f51409417a8737"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_c.dart", "hash": "7f1486a2bf169b977f3be1524f930a6e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/dots_triangle/dots_triangle.dart", "hash": "11089fef6a453ca0cfb25a16819dd61d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_platform_interface-1.0.3/lib/src/types/open_result.dart", "hash": "66b8e74d4042167c6cc4c43affd68c3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/samsung_galaxy_a50/frame.g.dart", "hash": "a96a24aa9438fff30fe5ea1a373a5eaa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/win32_registry.dart", "hash": "78f302720a7202dc38e0922492f5c7c3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sy.png", "hash": "2e33ad23bffc935e4a06128bc5519a2b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "22ad3e3602e0fc7a63682e56a5aeaac0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/src/devtool.dart", "hash": "2d7d80b5c908559a133f8729b6e755c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_variant.dart", "hash": "ce58628e17748af44a93e252b9c54d1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/ar_messages.dart", "hash": "3da2722743e4784a131240a19f44517e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "0e708f7885d57fccc31cdb5020c2d9c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/charts_theme.dart", "hash": "37f497804bd7d1ff55538058b2a494b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/utilities/span.dart", "hash": "abfab1ecabc1a50235f8b3661e51cfd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/any_of.dart", "hash": "8cd59827d2f99e2d6c62f2f38c275cf5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/_initialize_tessellator_io.dart", "hash": "4aa9532707ae3709836dac6e154dd477"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "7f662c8207cea5db3d45f239a277ca9c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "68c724edcc385ae2764308632abb76b4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gg.png", "hash": "cdb11f97802d458cfa686f33459f0d4b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/md.png", "hash": "7b273f5526b88ed0d632fd0fd8be63be"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "be7392100d4028793c499a48ed55cf29"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "97359ca5bc2635f947e7616f792565c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file-3.5.10/lib/open_file.dart", "hash": "a358ea467204682d64d905aac85e9a5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/utils/resolvable.dart", "hash": "f7329cc0811af555900320e49bd9686f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ci.png", "hash": "da9d5728161f286a4b5dfe34ff01a83a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cu.png", "hash": "8d4a05799ef3d6bbe07b241dd4398114"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/empty_points.dart", "hash": "6854c253df03b4791df243dc2409a59d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/pointer_data.dart", "hash": "67f58e8d946fa7138e52e482d7c8f8d5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mr.png", "hash": "733d747ba4ec8cf120d5ebc0852de34a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "9a67635cfd2e047d996c4840d4cb18ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.17/lib/src/loaders.dart", "hash": "93f4454ad1a70085b8e548f17997a5bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/wtsapi32.g.dart", "hash": "d6db794e2f414caa48650e6bc2396e6d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_1.g.dart", "hash": "902f4e12222d1e5b34d0ec77b2628f25"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bh.png", "hash": "6e48934b768705ca98a7d1e56422dc83"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bd.png", "hash": "5fbfa1a996e6da8ad4c5f09efc904798"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "20d5458a880a0a10253cda660dbc42e5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/to.png", "hash": "a93fdd2ace7777e70528936a135f1610"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/overdraw_optimizer.dart", "hash": "8345fecf6626eef2657a9b1f940f5d47"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "d2386b256656121d501a16234b008e2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationsynchronizedinputpattern.dart", "hash": "0a1c3f1481c65ee1018b56fe8d8b84ef"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "02dabe6a8cd832d69b4864626329ef30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/variant.dart", "hash": "68048a53f754265a484cc5c4798db6af"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "4b79250291135f96d72738feb6e5bc1e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cx.png", "hash": "65421207e2eb319ba84617290bf24082"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/s7.svg", "hash": "c2aab48e02ccc7028aafb0c31e432ba5"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/isensor.dart", "hash": "1093e13de26d6c3dd606a01c451762ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.15/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/client.dart", "hash": "ad1fa9cf20a00e4278dad17921649223"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/lzma/lzma_decoder.dart", "hash": "8e6aa847a915de368f0e2f98303878c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/lib/src/messages.g.dart", "hash": "07d545e5e568302b0453b8848be6a678"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/images/red_calibrate.png", "hash": "dd214b973df304ebac14e7f8aafd369c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/lib/src/models/pin_code_platform.dart", "hash": "8ded22a3614fb1f8d1a98313ccfa2cd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/four_rotating_dots/four_rotating_dots.dart", "hash": "d6d71e8d90831a45c858b94fd2bd6218"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "798f76b8076951e542aad4221a45d480"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/layout_handler.dart", "hash": "a85651ac046d20bffd797418f9af99d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/node.dart", "hash": "e6a5dc364972db7e4f38b5b49947b979"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/indicators/bollinger_bands_indicator.dart", "hash": "4134a3bd99c571eed86fa9d7d217233b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "d0ab7f5e11e48788c09b0d28a0376d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/barcodes_theme.dart", "hash": "df1efac1f3367ad8831cf25c8c1663b6"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/in.png", "hash": "c2e7c34b638189eb9b04774076e02e82"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/obb3.dart", "hash": "f7fd689f4549dd97ac670c72e4d617c6"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "57b508bc908fd0950889e1d70ce36fdd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/zh_cn_messages.dart", "hash": "df5104cc83ec9ed2205b381c4e3fbd4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispeechwaveformatex.dart", "hash": "919cc78cfaa28ec6b957a771cd0765ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/predicate/predicate.dart", "hash": "ec001ba2712f91cadae858bfdfe622e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/auth_options.dart", "hash": "859fb3f0e6ed9879424415ff38176c50"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "bc3c12f9555c86aa11866996e60c0ec9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "65c7fba34475056b1ca7d0ab2c855971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iinspectable.dart", "hash": "3fd143ba1c7f9f9098563ee5b342b240"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/um.png", "hash": "b1cb710eb57a54bc3eea8e4fba79b2c1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "b692d4a68a086507a66243761c3d21a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/win32.dart", "hash": "a2afa1345e294f0beeb9a776908eab25"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/lr.png", "hash": "1c159507670497f25537ad6f6d64f88d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/stretched_dots/stretched_dots.dart", "hash": "793c930c108bbb7fc4ef92eb50cd68fe"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "94dab76e00a7b1155b15796b87ebe506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_platform_interface-1.0.3/lib/open_file_platform_interface.dart", "hash": "b9472f50477090189a2b07927fef9874"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/abstract_file_handle.dart", "hash": "47c413b312340fd698a70078e845f701"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_f.dart", "hash": "01acde6ab3416626c8fe453d99c13480"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/misc/epsilon.dart", "hash": "b9283cabc57ae94b3c75f147903751fa"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "ee36aadc3fac54d5659c94c6aadcd007"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.2.3/lib/src/permission_handler_platform_interface.dart", "hash": "141e9b257bbdd7538861bb084fff79ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispellingerror.dart", "hash": "b78ba1985c8ec9afaa7beaa601fa8e00"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/services.dart", "hash": "bab8606629135509c96d78f7253526ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/generic/phone/frame.dart", "hash": "807d87dcf1f3add4a9dec9f6a615d4ef"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "94c0c017ccb267b7cacc7c047ee5b9c3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "a69e90f683dddaf61ae8d7f094219026"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "c7c757e0bcbf3ae68b5c4a97007ec0b9"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ye.png", "hash": "1d5dcbcbbc8de944c3db228f0c089569"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/pin_input_text_field.dart", "hash": "070960b9d447b1116c268532e29a4c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/sony_xperia_1_ii/device.dart", "hash": "877af1999206719040eb8cd1afbcb1d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector.dart", "hash": "7ba48caa7a6a4eac8330274dae899e48"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/tw.png", "hash": "88d58bc88a85dd550ddec586274e582e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/start_element.dart", "hash": "e11fc9210b4438654c11893b98ac66fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_e.dart", "hash": "012c3b670fb153803ce635838e1fa9ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/keyboard/virtual_keyboard.dart", "hash": "2c727f5733729c4a725a240dffeaaf1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_x.dart", "hash": "8c0609f71af975bf4d5197e6e0352a40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/decoration/pin_decoration.dart", "hash": "685dcb444255e7bd5056535e273d2475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/src/provider.dart", "hash": "a7efec55c58055a03130c41bdab65496"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/bar_series.dart", "hash": "5c80b87b759465f682f7b606caa894dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ca.png", "hash": "bc87852952310960507a2d2e590c92bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/flickr/flickr.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/dm.png", "hash": "b7ab53eeee4303e193ea1603f33b9c54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/constants_nodoc.dart", "hash": "caaa469735f17387a68b3b8964cfe18a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/string_stack.dart", "hash": "aa27dfc54687394062db977707839be5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/access_rights.dart", "hash": "571943578e78006ea11a53ba5a541224"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "498db9e29a08e6fdc8aee5eeb4d204ce"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/togglebg.png", "hash": "eee052a3ca9486f60447efcda0cdd227"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "03001d3ddae80bbf1f35c5e70e0d93e4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "7755bff1bceea0db42330320ad10baad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "1363e5e6d5efab4bae027262eff73765"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/url_launcher_string.dart", "hash": "ec94194f35d48443f468a3b06ef69845"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/src/change_notifier_provider.dart", "hash": "3ce0eeefa3058c1955fb1f435ce9928b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/lib/src/models/pin_theme.dart", "hash": "9d9c5929ef840ffd45554dcf97a8b3d7"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/foundation.dart", "hash": "b4a0affbd6f723dd36a2cc709535c192"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationelement7.dart", "hash": "cd0365e9895a1f44235bcf2288a11f66"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/kz.png", "hash": "cfce5cd7842ef8091b7c25b23c3bb069"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.2.3/lib/src/permission_status.dart", "hash": "79dd58834c82d30b84793b26e03d4ca9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/lib/path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "f59aed120736d81640750c612c8cfe5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/gdi32.g.dart", "hash": "3c738a2ffff7c2ec664bdd76408deff9"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/a3.svg", "hash": "7ec024a2cfa35391bd3e0e630a5461e7"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "ed582bff49cac60fb08ccee9ccc7c573"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationrangevaluepattern.dart", "hash": "f90b22ce5704e97441e7e2265d0119e7"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "4f9995e04ebf5827d1352afca6adda26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/tool_panel/widgets/target_platform_icon.dart", "hash": "f7377b62a6b46380cdffdefa17cbf385"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "ea5bbc17f187d311ef6dcfa764927c9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/idispatch.dart", "hash": "04722e21ad1b67baca7f75a984b0d2f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/frustum.dart", "hash": "218ecb2798a6fb1ec08cd5c993d98269"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/column_series.dart", "hash": "51f3a2b069f0e84a3e650f3ebaed7fcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationvaluepattern.dart", "hash": "ede54fd11e6d44588748f07a8711f863"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/stacked_line100_series.dart", "hash": "f697653b6bbcc5de8cda4147d049dd5d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "e40877daa15509fcbd3e465d246dbc09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/pattern/pattern_iterable.dart", "hash": "f0ae0acd94eb48615e14f6c4d1f5b8e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/generic/tablet/frame.dart", "hash": "095f2968fb125e0c8c9d88840ac71952"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gf.png", "hash": "71678ea3b4a8eeabd1e64a60eece4256"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/models.dart", "hash": "792752250b7a3d3bdb27d7616c40819a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_android-1.0.6/lib/open_file_android.dart", "hash": "487b995ad87fb1bba2fb9d6bdd9b11da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_windows-0.0.3/LICENSE", "hash": "54ca5f4345f518f70f047f1f2e585945"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/exceptions.dart", "hash": "7c49607737f1eac9820d787b1f2854eb"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "e461dc9f79fcf6a9e4faf12c8182fb47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/sparkline/utils/helper.dart", "hash": "44644a42732e588dadd7aa46c237419e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationcustomnavigationpattern.dart", "hash": "f71a5e0c2e702bd1f70b7f60ac19eec3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "a89f6417642d57961ee87743be4a6a2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/crc64.dart", "hash": "338a8eab73f4990e8d6aaa47f9547f2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/immdevice.dart", "hash": "545e435076682f57181d79d48821ae5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/tool_panel/sections/device.dart", "hash": "e30cc57ae3e852be54c2d79b41a0d805"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/macos/devices.dart", "hash": "f8f18f0121f339fe3bb617ea7daab3db"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "15957b9d3eac4a2e1acaa24a3032afe7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/td.png", "hash": "51b129223db46adc71f9df00c93c2868"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/compact_number_format.dart", "hash": "9068f4d63af1ec44245b76b7ab4dfa48"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "166147b7bee5919995e69f8ca3e69d17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxfactory.dart", "hash": "aa34ef78c82b66e4c309bd5f4973e3c0"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/sheet.dart", "hash": "0e0b94d805e193b69802ca99d5a51b27"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gr.png", "hash": "86aeb970a79aa561187fa8162aee2938"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispeechobjecttokens.dart", "hash": "2b6a616f4d89d2cc1f4b1004a5e58085"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/src/bloc_consumer.dart", "hash": "cfeef5bef4b38eabcdc4d59980790787"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ni.png", "hash": "6985ed1381cb33a5390258795f72e95a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/single_child_widget.dart", "hash": "9116cfed7a58a3a51ac19c4b887bfa90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/setupapi.g.dart", "hash": "ec1ebd4c36e474539966b09b9d152fd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_h.dart", "hash": "fcfe1d3dbdb081cdeca153aebf6667ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/src/bloc_builder.dart", "hash": "108a68735e9003260dc364b785e21a1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/src/value_listenable_provider.dart", "hash": "bbd255fe46712b372dfe3b99cb340068"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "59cca02e92c0ff79aac0c54c50e3bd2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/exception.dart", "hash": "773da8c184ab316ec6998980a1448a1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/inline.dart", "hash": "7f107258e4c6ceef750c5b59f287068f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestapplication.dart", "hash": "3dc4006aab4c069db52d46f59e8262fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/choice.dart", "hash": "8c5e16920bcaf0c7e7b82d0d0871ee56"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "3de98898d0fea89f0e609dcbf7b69783"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/plane.dart", "hash": "2a0078c9098cdc6357cbe70ce1642224"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/type_conversion.dart", "hash": "032c93433e86ca78b8bb93e654c620e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.3/lib/options/macos_options.dart", "hash": "ef56d0c30c2ebbf770de5c7e9cd6f6a7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/no.png", "hash": "f7f33a43528edcdbbe5f669b538bee2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ishellfolder.dart", "hash": "a1616e35cb9fc80b351d84aea1626b36"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/a6.svg", "hash": "8e82073ea9d0d268ce1427fb1281d818"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/behaviors/crosshair.dart", "hash": "a084cfcc22655d3558a24fff00d27d1e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/tj.png", "hash": "adbf9279580a5c9eca9f6f108b50f2ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/np.png", "hash": "35e3d64e59650e1f1cf909f5c6d85176"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/misc/failure.dart", "hash": "2db6cf613a3f03e05a2c19ca6e14447b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ms.png", "hash": "32daa6ee99335b73cb3c7519cfd14a61"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/theme.dart", "hash": "9f4446a51f61352ab9a13bd40184bb5f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/vu.png", "hash": "ffbcd1822588e63b3ef0f2fcb230b179"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "a340eddbf129cfd60e2c67db33c6003e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/vector_instructions.dart", "hash": "4a85886272faf2318070437f1c4be381"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/format_exception.dart", "hash": "2128831f60d3870d6790e019887e77ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/save/self_correct_span.dart", "hash": "a2da01a78c9486135afc46cb84ab658e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_address.dart", "hash": "4ecc0e7678d4ed3bf62a04b3e383e424"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gy.png", "hash": "75f8dd61ddedb3cf595075e64fc80432"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/pattern/parser_pattern.dart", "hash": "79a5f25a1a9d4aa4689bf37171e1b615"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gq.png", "hash": "0dc3ca0cda7dfca81244e1571a4c466c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/py.png", "hash": "6bb880f2dd24622093ac59d4959ae70d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationdroptargetpattern.dart", "hash": "1ea35c2990caf75b07d8a555f3f49191"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gl.png", "hash": "d09f355715f608263cf0ceecd3c910ed"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "ebef4cfdfb854b138f6bdbbf53e73f0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/date_computation.dart", "hash": "37837bd1379e66f38e4a7775b6084d0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationnotcondition.dart", "hash": "6aa37695d4ecfd1cd9514e2758da9f5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_k.dart", "hash": "de8b58c147e392ac3e1a5479f4941290"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/co.png", "hash": "e2fa18bb920565594a0e62427540163c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "625b858bd9847eab75d2f3f6295a25bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name.dart", "hash": "da50c399c40281c66d3c2582ac225276"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "23f5fb6033bd02c94d263d1ed41fb90e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "aed826e965e4aa2fdb3466d39e33d824"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "22cb97b7d09f329bab7ed148b4d181e4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gw.png", "hash": "25bc1b5542dadf2992b025695baf056c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/cdata.dart", "hash": "a1bc06d1d53e9b47b32fbdb4d323f44d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/vi_messages.dart", "hash": "a7eaabaee9e45b9de35e06b27e3728b3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "b6e992b1127f8376358e27027ea7a2ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/normalizer.dart", "hash": "5684bfb4916cd4ec19596b3fd6a7a93b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/getsid_windows.dart", "hash": "659cff14f1665a31dec63407d7839624"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/tm.png", "hash": "3fe5e44793aad4e8997c175bc72fda06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/utilities/colors.dart", "hash": "3f746dfd8ed07f0b539c5434c1965607"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationselectionitempattern.dart", "hash": "385e7301c1c09d5c45f0531a5c375c6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "4e04af41f89adf9231bad1579f5bb9a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationannotationpattern.dart", "hash": "2a397f62f7c1670044f38d8f4af1ec92"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/de.png", "hash": "1b22a85ae7290aae77b9270667a57c07"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/utils/pok.dart", "hash": "075edf06e5f53a8bb3dabdf1b0e1c420"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/images/appicon.png", "hash": "f13e54be2a299ec3c7f7912d76c67e3e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "3d7501e746aaa83cd9cc1b508d3f7ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iclassfactory.dart", "hash": "cd0faf95b7346ac8469c545bef368396"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ishellitemimagefactory.dart", "hash": "a966fe9730c6e36f9a0123b9eb1ae505"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/error_helpers.dart", "hash": "73c0a59e2d19aea71c6029f871aa9f67"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "7d5bd66d61c58afe63c6d33ee0e421c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/sparkline/marker.dart", "hash": "400903038edd30673c2657fede0fc565"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/tz.png", "hash": "97b962f0a195c567aa959f2fb2004e03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "2e074f4fb954a719546377c67cb54608"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iknownfoldermanager.dart", "hash": "48d51a5672af342c6b1376d1ff04a4a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ae1f6fe977a287d316ee841eadf00c2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/LICENSE", "hash": "4af3ef7a7a3802b5175cf72a7a8a47ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_q.dart", "hash": "e126494233cc791fd4f817e26948cb99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomation6.dart", "hash": "3a7c0d6ff07fca442df7724d853dfbe0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/equatable.dart", "hash": "1a5f064d497f9539e8e2cb4ba15a8f05"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "e472fd233266592e97b3fb39bb1a11dd"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sg.png", "hash": "94ea82acf1aa0ea96f58c6b0cd1ed452"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib/model/base_device_info.dart", "hash": "4f0e33807b3ea8a3a5d03a85dbdf565f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/api_ms_win_core_sysinfo_l1_2_3.g.dart", "hash": "f22a66d83ebf3e0455559a16f49e70bd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "edbd68eb36df4f06299204439c771edd"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/eh.png", "hash": "f781a34a88fa0adf175e3aad358575ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_writer.dart", "hash": "0537fb6d3d370f2fd868e2654361356a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "83df4f6e4084a06a4f98c27a524cc505"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/tz.png", "hash": "97b962f0a195c567aa959f2fb2004e03"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bb.png", "hash": "a5bb4503d41e97c08b2d4a9dd934fa30"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "ee2f417f35b5caa4a784b24c1bc32026"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cl.png", "hash": "658cdc5c9fd73213495f1800ce1e2b78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/r_sizedbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/geometry/pattern.dart", "hash": "75cd4f61b777cfcb29a03be16c612f1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/utilities/json_converters.dart", "hash": "db74976be1602c73440a68331d06ac1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad_air_4/frame.g.dart", "hash": "978a094a7d41104d52d66a20b0b276d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/widgets/draw_triangle.dart", "hash": "b8db72e8db4dca5ab78a7b387f1ad0f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/local_auth_platform_interface.dart", "hash": "cbdfe985d2a7b78662d58e05cd1dddcf"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bn.png", "hash": "94d863533155418d07a607b52ca1b701"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/trendline/trendline.dart", "hash": "335ebe6f1914ce88ac1149c56ac768fb"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "7b28ec35aed9cbc3319bf4c15d7b352a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "bda2eeb24233fd6f95dc5061b8bf3dd5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/stretched_dots/build_dot.dart", "hash": "06877fde216f0b9089144ef14e4ea012"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/parser.dart", "hash": "7f364a339192e22069e77ad3ff05d2cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/itypeinfo.dart", "hash": "2fe7a01e6cccd3fc371fd2d730935afe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/_tessellator_ffi.dart", "hash": "5d6c8b8c3579aa8f4922fb58003e4153"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ibindctx.dart", "hash": "3af3fd07f4a1feeb62307f54d5bd0aaf"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bw.png", "hash": "04fa1f47fc150e7e10938a2f497795be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/src/generated/glyph_set.dart", "hash": "8a451864f1a46f19700d46fc5d4cbd39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "d2372e0fb5a584dcd1304d52e64d3f17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/permute.dart", "hash": "a857d1061019a3f9214e8735890c300c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "cd995d0f309bf74d0bbe94eb1e4e8e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/kernel32.g.dart", "hash": "edf7bceb5006082ec684ee177cdf3025"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/hilo_open_close_series.dart", "hash": "1b5b0194d2c7124c26f5f80ff3c15c47"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/fj.png", "hash": "6030dc579525663142e3e8e04db80154"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.2.3/lib/src/permissions.dart", "hash": "53f73ea449c4c7ed1b356f8d0ea37829"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "9434ff8aa06e13d5981ed6ec15eceb64"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "737642bf1a2d9ebd63c82016292b6b93"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "bef4f4d150af7d7e46b13da4847f86fa"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/tn.png", "hash": "87f591537e0a5f01bb10fe941798d4e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_breadcrumb-1.0.1/lib/src/breadcrumb_item.dart", "hash": "02fafe2aef583c20ca3d251c2e3e5ccf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/xz_encoder.dart", "hash": "844b276aac80044a3bf3ff7391ea7614"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/shared/types.dart", "hash": "7e327134a49991d7ba65bbfe46bb8f4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/lib/src/models/dialog_config.dart", "hash": "0a5c21a7973ca88f987196b59919c023"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/method_channel_url_launcher.dart", "hash": "351ed98071b53d3c2e98d376f2a65a74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/expression/builder.dart", "hash": "9f3c00f460ef40c2f330a850cde3101d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/core.dart", "hash": "2edce71d13a627858ee90577d3eae6f0"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "830b9f37313c1b493247c6e7f5f79481"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "c51e8dd4b4c6fc4a085adda93a75fdc6"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/hk.png", "hash": "51df04cf3db3aefd1778761c25a697dd"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "9c053b0efcabd70996cc27e9d6c9303e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sj.png", "hash": "f7f33a43528edcdbbe5f669b538bee2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/quad.dart", "hash": "9a043d96e7ae40786de66219219bea4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/none_of.dart", "hash": "2080f99186cef2d2ec3f4c6c5b7c768b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/stacked_column_series.dart", "hash": "2bea5a5f6e31621b882f8ec76a11758c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/xz_decoder.dart", "hash": "c65d4cb52a62688ccb3b832f190386c8"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "bb7bcb463df2ae0f5f952d439fdb384e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "0f61d8c0c0870ae724b64f2f2af816bc"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gu.png", "hash": "babddec7750bad459ca1289d7ac7fc6a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mu.png", "hash": "aec293ef26a9df356ea2f034927b0a74"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/dropdown_button2/lib/src/enums.dart", "hash": "acbd0b343a8563baa921fef8bd62f3ee"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "deedcf7ee9b4e76191202e61654f9dcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/LICENSE", "hash": "0dc6cec7b4ef07b48069a9ff484a8ffe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml_events.dart", "hash": "71b9da53ac40a568e55525799518d891"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "62b4a318d3ec0d03d3dc78b84cf0458a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "e7b2de136a99cf5253477d4fb4138394"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/interactions/selection.dart", "hash": "188cd5aced4f379678728c47a790da06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomation4.dart", "hash": "beb5454dc4d32af79b6177c6ef646714"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispellcheckerchangedeventhandler.dart", "hash": "e82d109f954c4a736896b202eba01ce1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/br.png", "hash": "8fa9d6f8a64981d554e48f125c59c924"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "e4c4603e78131a8bc950a8029d624a76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bi.png", "hash": "fb60b979ef7d78391bb32896af8b7021"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/rs.png", "hash": "d3e3217aabf6387166320514841c14ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/lv_messages.dart", "hash": "07c4da4841ab6a2c4f3aa74d6cba63ae"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/ku_messages.dart", "hash": "e527b1ce072d160152032269b934a2d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestproperties.dart", "hash": "3ec463d588e64344f9c833041d4c2e74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/archive_exception.dart", "hash": "961bcf3872c8a50d7233563a0f5228ff"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "083722b0880e8e5981f9e33da11e449c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ag.png", "hash": "9bae91983418f15d9b8ffda5ba340c4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document.dart", "hash": "0f90625420cd7d017be4426f0bdaf0e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib_encoder.dart", "hash": "d377631c264a3691da5a661b7fb4d60b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ishellitemresources.dart", "hash": "6f452535b56a9cdc6bc36bd647963dca"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "52d0e96cbfe8e9c66aa40999df84bfa9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "782760e5709624f38ebac3b7c728a792"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "3c24303086312d7181ffa10d0521029a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/word.dart", "hash": "05e847132bc525d82c8f22363faaab59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/hilo_series.dart", "hash": "4e591ea367bded5d29c87ab26b5deb43"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cc.png", "hash": "126eedd79580be7279fec9bb78add64d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/et.png", "hash": "2c5eec0cda6655b5228fe0e9db763a8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/samsung_galaxy_note20_ultra/frame.g.dart", "hash": "9507c1ebbdec11fb5514e2f30470f184"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file-3.5.10/LICENSE", "hash": "54ca5f4345f518f70f047f1f2e585945"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/a3.svg", "hash": "7ec024a2cfa35391bd3e0e630a5461e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/extensions/set_string_array.dart", "hash": "dce5e400c1f0958583196f9db05de7b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/decoration/decoration_circle.dart", "hash": "cb07d33f4b3aa4d5d47287e0b88162c9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "d72a4ddaf6162d8b897954e02b4a2a4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/screenutil_init.dart", "hash": "81645f9b5649a11f5ddd2da06a273a2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/doctype.dart", "hash": "c2d76b78fb107e358b1ad967f15f1746"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "b61a261e42de1512c8a95fd52ef6540d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "d9dd226ec96aec60f125c0f1f8d00344"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "0434e70443094435172ff3d214d26bba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/date_format_field.dart", "hash": "53b1a2074650b8f2808e620e2b9ddc41"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ne.png", "hash": "a152defcfb049fa960c29098c08e3cd3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/es.png", "hash": "e180e29212048d64951449cc80631440"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/_zlib_decoder_io.dart", "hash": "1a51de29eecde3f77e18012772a2991a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtextrange2.dart", "hash": "afc3af0d3aaf8d64a82177a094097ee9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/spline_series.dart", "hash": "b64b49ee7e57e5f0000a2d559bec627b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/utf16.dart", "hash": "07d628617431f09942070c95c65d241f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/comment.dart", "hash": "74fb000405fb96842a3ce15a519d8ae8"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/au.png", "hash": "600835121397ea512cea1f3204278329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/dispatcher.dart", "hash": "44c4e1de5f30ee41e94e0017dbd5f035"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/rw.png", "hash": "6ef05d29d0cded56482b1ad17f49e186"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/src/messages.g.dart", "hash": "43f414cb7bdf200801888b9a62c38f7f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/themetoggle.png", "hash": "4d3d2c6df1e9648dc47be8bd1af8e3f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad_air_4/device.dart", "hash": "251e47ac8cbf23755dff6df87a7449bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/cursor/pin_cursor.dart", "hash": "e780afe47ecaa91df50e649ec372d6b3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/kr.png", "hash": "9e2a9c7ae07cf8977e8f01200ee2912e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/hamburger.svg", "hash": "8d738d797a4e5a2a1a113c53cf2cb3d3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "2fe7a1026669f97031a83f6da44d248b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/LICENSE", "hash": "dabf5f8fca77a12772e9e2699a075104"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/az.png", "hash": "967d8ee83bfe2f84234525feab9d1d4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iwbemconfigurerefresher.dart", "hash": "24c932dcdfa3c21be567bbe9dd305845"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/id.png", "hash": "78d94c7d31fed988e9b92279895d8b05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/axis/logarithmic_axis.dart", "hash": "376e6acf5eb1b49429bb591f57c1132c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/utils/constants.dart", "hash": "a51a48dce4668bc40c51dd0d6e7c2899"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/geometry/basic_types.dart", "hash": "62ed7bb0dcdc2c9125d24015145427a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/definition.dart", "hash": "1a391f72b5be0846aec18bcf1609b962"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/theme.freezed.dart", "hash": "f4cdf1b446ea4ea6291761e0c2245998"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/callbacks.dart", "hash": "f614c0ac0f1a58c3ad8f99e86f09c997"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "9e64d24aeed0ce5534422c6e4b454676"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/si.png", "hash": "922d047a95387277f84fdc246f0a8d11"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "247fd4320e1e277acc190092bf6d35ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "eb89408ce23b2abcd324ea5afb05a1ea"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/containers/CustomMultipleSelectorHorizontal.dart", "hash": "d8fe711e2eac138dc7e8ee00147b9680"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/hn.png", "hash": "09ca9da67a9c84f4fc25f96746162f3c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "4591f6273e6282466c0364d5331e50c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/character_data_parser.dart", "hash": "a3044567a5c6d8b0e52367af1a23d5e1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/tv.png", "hash": "493c543f07de75f222d8a76506c57989"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/pm.png", "hash": "6cd39fe5669a38f6e33bffc7b697bab2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/LICENSE", "hash": "173b5ed8cb40837e898de649567af59b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.46/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/lib/src/local_auth.dart", "hash": "1feadb70762e4b4efb3c01f222821fe0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/th_messages.dart", "hash": "2c12f17c825caec63da5c9490b2ab38f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "b0aac7d00e469646d25550d1e4e77d12"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/s1.svg", "hash": "e1f3e78d6dd2ff8449f5669a298bee19"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mu.png", "hash": "aec293ef26a9df356ea2f034927b0a74"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cn.png", "hash": "6b8c353044ef5e29631279e0afc1a8c3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "203fbbac922589879ae44083b04a368b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "9e353a749332f6cfdbe6f0d07ff17f5f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/tj.png", "hash": "adbf9279580a5c9eca9f6f108b50f2ec"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "a2ab6e0f334e5a28af29766b82f7f4b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad_pro_12Inches_gen2/frame.g.dart", "hash": "ecf612ec182f579e34b034cb09a67384"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/containers/CustomDropDown.dart", "hash": "b56e9dd6251d40cf3732158f187850a7"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "58707cf455f97f907192b4ff92d36711"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/winscard.g.dart", "hash": "77ba184cb297cb10af7a4e645e26b0ef"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "8199cdd8c075bef2ed0811394702680d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_encoder.dart", "hash": "df916478c93bc4bc0bc9adb2cc286153"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "88dbcce51623c5bb2cbe1e4a0f80a902"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/horizontal_rotating_dots/horizontal_rotating_dots.dart", "hash": "10ac6dacf6f344b0161d1ce78f99706f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "98772211ffa69a8340f8088cd7193398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/wave_dots/wave_dots.dart", "hash": "f3ac8ad44b00dc811fc3df1d887b6d9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/flutter_secure_storage_windows.dart", "hash": "141745c6e29022622def8ba527cfd60c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable.dart", "hash": "52138432903419f8457bcad45e5e6e99"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/whitespace.dart", "hash": "f0df878443ef28db864b73e66f8206a2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "777aca422776ac8e4455ccc7958f7972"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/types.dart", "hash": "13e6a7389032c839146b93656e2dd7a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/number_symbols_data.dart", "hash": "f176d4d0e0b6d9e454dc1b0f0498507a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispeechvoicestatus.dart", "hash": "f242cfdba2fc6ad938c53befa4c2050c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/utilities/assert_inherited_media_query.dart", "hash": "f5c78b6f1bbf5bc734f9a201a8fe753c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/eg.png", "hash": "9e371179452499f2ba7b3c5ff47bec69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/lib/src/cursor_painter.dart", "hash": "2e99ea9867661b3c1e5b30fcacdd7500"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mx.png", "hash": "b69db8e7f14b18ddd0e3769f28137552"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/user32.g.dart", "hash": "e2d16ea1496afeed39828f05f60debd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/r_padding.dart", "hash": "72c755438ac184c534cebeb8b335ed97"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "1d893e6d648c41d8e3281a76a2320431"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/extensions/dialogs.dart", "hash": "ca0e62303e3d1154ac7712e05d705c03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "2be9783170f41208ab65361d7cb0ddc4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/va.png", "hash": "cfbf48f8fcaded75f186d10e9d1408fd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "05c5ca73bc4e912f53a324cfa508bbfe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/range.dart", "hash": "065ae19274f3691df673e4d2a11f5d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispeechbasestream.dart", "hash": "095d62c8e0367fb3c65fa8c828e95c4e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "f90fd4f8a9988f08157d132c23c8c08d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/lib/src/charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_decoder.dart", "hash": "86727853a00a22df95e85607270896f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/storage/preferences/preferences.dart", "hash": "a7595422939b3c2c508424decac3d421"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "9d2e926705e7e23b2e34aa022cf55324"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "8c1a2c1feaeb22027ba291f1d38c4890"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/na.png", "hash": "3499146c4205c019196f8a0f7a7aa156"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/ro_messages.dart", "hash": "08d5518130c41be82a3bedc95abaf928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/tool_panel/sections/subsections/device_model.dart", "hash": "96f6c2bf934ca99663acf58079cf6d36"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cg.png", "hash": "1f1b349f054a4183e80149268c7c4d31"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/drawers/profile/country_code_picker2.dart", "hash": "dec9443fde17d29bdc172d77a3dc92b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.3/lib/options/windows_options.dart", "hash": "b4355b7f9f9e50017ce52a8bda654dd1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "fab9f5f0fb3bdd9295e12a17fef271c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib/src/emitter.dart", "hash": "50e530584d1bea06417dad772702cd7f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "ff7c5f41b6493392c45ef30383f6af9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ai.png", "hash": "cfb0f715fc17e9d7c8662987fbe30867"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ru.png", "hash": "9a3b50fcf2f7ae2c33aa48b91ab6cd85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/extensions/list_to_blob.dart", "hash": "56d7144236503f311a7d9a966eaf2fbd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/authentication_challenge.dart", "hash": "7bfefcc0929d945fa61bb7870de1f659"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationdockpattern.dart", "hash": "e05a31b36d602ae06ddd1979c05df7a1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ug.png", "hash": "dd8c47c7243ac213a236c3abdfd416c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/lib/freezed_annotation.dart", "hash": "e0edb55e8495e5c2208d8505329333cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "98cd866294c42f2faff3451e5ca74bfa"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/uz.png", "hash": "d3713ea19c37aaf94975c3354edd7bb7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/containers/BillingFormula.dart", "hash": "d9aa87bc4ecd0616a81cdf8143a8c3b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestreader.dart", "hash": "bcd1f230f11ee46b0ed40d340d9591c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ichannelaudiovolume.dart", "hash": "8ccaa7ec037755c10bf5586831be0fe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/immdevicecollection.dart", "hash": "5c53c4dc5952c49c1b6ccb65674d9072"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/masking_optimizer.dart", "hash": "0a91e9bccfc0453430b69cfa8ff8caf9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "0c38ab3123facc4ec6f01ba31158c3ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/utilities/archive.dart", "hash": "9ddddfc4ac574565fadc4707de93c545"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cv.png", "hash": "60d75c9d0e0cd186bb1b70375c797a0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/bidi.dart", "hash": "68634d4df864077f507d84d92953a99b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtableitempattern.dart", "hash": "d004b4e52622f42ec84eea09ede49f43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/list.dart", "hash": "ee730199a496cacbfd82312849e80523"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/pages/EnterTwoFacCodeSignUp.dart", "hash": "efdb68590ae991745d5260fa431c3bcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/decoration/decoration_boxloose.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.1/lib/src/shared_preferences_async.dart", "hash": "255fd9cb9db57da2261cb7553da325ab"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "2ad27cdee5e6fe69626594543bd0e7c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_v.dart", "hash": "4cb87d15a1cc8c482587425775418f04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/build_loading_animation.dart", "hash": "0709425754575233f08b840e32ad5b03"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "98f725d06ba20a1032cb8770d00d7fca"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "270de9c98f9c1284da0a6af9176ee1f9"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/co.png", "hash": "e2fa18bb920565594a0e62427540163c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "bd3f0349089d88d3cd79ffed23e9163b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationelement2.dart", "hash": "a67676334dcb7629a485b52714780808"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/im.png", "hash": "17ddc1376b22998731ccc5295ba9db1c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sx.png", "hash": "8fce7986b531ff8936540ad1155a5df5"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "e1a148a465b713a6366d5a22a1425926"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "10505aa641207501d9a0759bf2d6515e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "d7a6c07c0b77c6d7e5f71ff3d28b86bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/indicators/rsi_indicator.dart", "hash": "75a42519ec2f41064c4580d77194580e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/jo.png", "hash": "d5bfa96801b7ed670ad1be55a7bd94ed"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/im.png", "hash": "17ddc1376b22998731ccc5295ba9db1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_read_buffer.dart", "hash": "fd517e61edeaf09f9e4cf9e9ba8af13c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/inetworkconnection.dart", "hash": "51bc9f87faab4993239b12e26047c819"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/iq.png", "hash": "dc9f3e8ab93b20c33f4a4852c162dc1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_auth_client.dart", "hash": "e7b16979b9242f704712f406b29a96d5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/be.png", "hash": "498270989eaefce71c393075c6e154c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/visitor.dart", "hash": "87e0c94a0dd945f819a8bd24a9ac5e67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudiostreamvolume.dart", "hash": "eb9a74dc716d537ceafdd2a40b884df5"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "555fcdeebbe6517cde1cdd95133cabd7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ad.png", "hash": "5be9e620c31e271a032ccaf984c4185f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "269af8ca7030ccfd9c868fe9af8a6b0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/imetadatatables2.dart", "hash": "bb9a6b8b9225608821735003ffdc8a5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispeventsource.dart", "hash": "761edf39926ba43b2d6c95d677bad6ab"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/profile2.svg", "hash": "8c79675c696b4c46bbedbd13735d714a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_9.dart", "hash": "e7280a239aa04ba649fc950d8170cb3f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/pl.png", "hash": "a7b46e3dcd5571d40c3fa8b62b1f334a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/named_entities.dart", "hash": "c7e489fa5d00c1717fe499f3845c2abb"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/images/basic_advance2.png", "hash": "be17da8c0ad0fbfa77620361279ca89b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/input_stream.dart", "hash": "98fb8718913406fcd5a6d334e134c448"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/li.png", "hash": "c3bf7d5c792357ad1eb6e7cede352c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512_fastsinks.dart", "hash": "7924bc2d95999b2767d9f34e6ac52f98"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "cd7a7fd807697152dfdaeb3109e4f4f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/samsung_galaxy_a50/device.dart", "hash": "36af854be6a1806c53bcc3071a502ed3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationspreadsheetpattern.dart", "hash": "c46a3b47927574d4a8ab22690e395c2e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/sr_messages.dart", "hash": "9edf92861d576bdf045c42ebe808c558"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a2d1c7bec7b52901761f3d52a1ac02d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/utils/helper.dart", "hash": "aad481a654c608a6821cc1f34acb96ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_platform_interface.dart", "hash": "59bb1cba1648db956dccb835713d77d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/triangle.dart", "hash": "e3f9a51488bca91a3350831c8ad6722f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/samsung_galaxy_note20/device.dart", "hash": "31ecfd3aac600f4f3a5f2f13b29661b4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "91d8303ca1ccc72eccc1ae636c7825ed"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/lib/src/country_code.dart", "hash": "ee60e442dd3fe6cb798e6a87db14731b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/a2.svg", "hash": "1b24c777f385037b1767ee85a330aeb1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "a8513860b3b4c160b57ca6264bc0acf8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_android-1.0.6/LICENSE", "hash": "54ca5f4345f518f70f047f1f2e585945"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/tl.png", "hash": "b3475faa9840f875e5ec38b0e6a6c170"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/tr.png", "hash": "0100620dedad6034185d0d53f80287bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bq.png", "hash": "67f4705e96d15041566913d30b00b127"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/winmm.g.dart", "hash": "b670f26b5ebe125326b4ceadf218d1fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_value.dart", "hash": "6f3422c300e4f005e63a4246631f3372"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/state/store.dart", "hash": "a4f6b8eccfe493e2dfb7e980cc193103"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/je.png", "hash": "8d6482f71bd0728025134398fc7c6e58"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "9b76b249fb23172215a62d66bb393ed5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/date_symbol_data_custom.dart", "hash": "dc529c6aa777920dc7405c4f68c3d68e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/lib/src/country_localizations.dart", "hash": "0007b580cba9751cdf0eb8be8f866d85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_s.dart", "hash": "2d79382537f3ba898ab7a80cd0fbf0ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "c165bb259eb18a2dc493a0e7a1d1ebd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_switch-0.3.2/LICENSE", "hash": "c70687a8e69200de4ab9de54d4f50056"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "78a0faeef5f0e801943acdca3f98393d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "7b70bab5ac21ac24af3c971965617277"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zip/zip_directory.dart", "hash": "457372a62869445ee1cff9a92d5746a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/flutter_secure_storage_platform_interface.dart", "hash": "8dac3815609f98dfefa968bc2ea4a408"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/ko_messages.dart", "hash": "bbcfcebc98d822265508d95c7f9b4f27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispellchecker2.dart", "hash": "60ed6e3dc269f179875fea840112bc4c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "f90beedee11a434d706e3152bfb2fd15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/dbus.dart", "hash": "03dcfacba7168ffc4866191154e27f29"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "04c713cbc0ac5e15c7978a2e91b81488"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "f6d18a38c0986111a3d297424ed6fbcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iwbemservices.dart", "hash": "58ebbd139a7de7bef2e2e646cdb00d7e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/cast.dart", "hash": "dc379ed249557649f50b9c27d0033be6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/src/repository_provider.dart", "hash": "c55676f15a02286fe6c6ca65d8b8b1f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/device_preview.dart", "hash": "2cf56aba1ea27505184e76e975d5fc0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/enums.g.dart", "hash": "68763b18d67fc053a444301a9deffb33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/bzip2/bzip2.dart", "hash": "c452407613e1b5e2deaba24159998ced"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "256d1c386e48e198e2e0a04345221477"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/es.png", "hash": "e180e29212048d64951449cc80631440"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/cartesian_chart.dart", "hash": "149f8551391214d87c346779d589c717"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "f350db07fdddbcfd71c7972bf3d13488"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ro.png", "hash": "1ee3ca39dbe79f78d7fa903e65161fdb"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "1fc85ca774e46295ca83c157718278e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/android_device_info.dart", "hash": "9bf109ecb182b597064ca9ae6f68b801"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "28d3a26c44687480bac3f72c07233bf6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/storage/file/file_io.dart", "hash": "fdd0058aa748beacc1df1c02bf1c6e09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationandcondition.dart", "hash": "698f215aeb2c56fc2970fa91499d8b77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/and.dart", "hash": "1e9ed9cdf00b9449d9b72dcd00add4d3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ht.png", "hash": "009d5c3627c89310bd25522b636b09bf"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gb-eng.png", "hash": "0b05e615c5a3feee707a4d72009cd767"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "428549777327ddf7f2287b69cab7b68b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "9c169d41e4740bbc21d0ce33bc753119"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "3623c605586d2e37af23d6b746721bd7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/wf.png", "hash": "4d33c71f87a33e47a0e466191c4eb3db"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/dj.png", "hash": "dc144d9502e4edb3e392d67965f7583e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/drawers/profile/ProfileDrawer.dart", "hash": "01b1a96f5d1e6bec966c40c114fa1e8e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/images/2falogo.svg", "hash": "ba696a5958a18c4f033087f43ee41a0b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ly.png", "hash": "561134dc2f91764ed93227639cc7f599"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/pa.png", "hash": "49d53d64564555ea5976c20ea9365ea6"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "9ec81b597c30280806033b70e953b14c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/pw.png", "hash": "92ec1edf965de757bc3cca816f4cebbd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_remote_object.dart", "hash": "4f187fc37cb2a7eedf4681e2321792f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ifiledialog2.dart", "hash": "f45b881803064da6852bd34e8ef7951c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/s2.svg", "hash": "e83f818a47b01b8667cbba2f5a0996d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ipersiststream.dart", "hash": "7c0ee8dc84c442f69b0970bb8534d740"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/colors.dart", "hash": "9cd03844c4e859875c10c9708556a0db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/core/token.dart", "hash": "595737cf044c5d483e4615a1b0e1db71"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "fad2940dc1f4f3e4a0ebb5c7ff40a3a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_value.dart", "hash": "002be4c072c0cc5c5e72b5ff6d0c490b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/null_mapping.dart", "hash": "4bc463f9c4b5496d8918b58070c10515"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/hr.png", "hash": "04cfd167b9564faae3b2dd3ef13a0794"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/candle_series.dart", "hash": "bd5a2f3f80f006b90e3c0d94a37a3e80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib/src/path_segment_type.dart", "hash": "b46578a0f8f94ea4767f634b5235a54e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/LICENSE", "hash": "815ca599c9df247a0c7f619bab123dad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/ray.dart", "hash": "5d9bdad87735a99fb4a503c5bee7c7fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_breadcrumb-1.0.1/LICENSE", "hash": "39abf1ea18e65be22778f292bd7b71af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/ur_messages.dart", "hash": "c5338935b45474ea94cf777a85c4bb73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/visitor.dart", "hash": "27780bbb98adce3f00386fc6223bf2c9"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/la.png", "hash": "8c88d02c3824eea33af66723d41bb144"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/as.png", "hash": "830d17d172d2626e13bc6397befa74f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestreader2.dart", "hash": "a109a0fbd62a37b4cf3b416da4411578"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationscrollpattern.dart", "hash": "106d1bdb4f9d839cf4a65252635f965c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/model/filterAndSummaryForProject.dart", "hash": "b90887caaf6f689599e1a71b9badff83"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "2539eaeb4e2f2f69f678fd850c2332e8"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mm.png", "hash": "b664dc1c591c3bf34ad4fd223922a439"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter_localizations/lib/src/cupertino_localizations.dart", "hash": "4b64862d7017b3b2e105435437ab5d88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/tar/tar_file.dart", "hash": "4fb326b6cafb49f8489cb571ac74c923"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/axis/plot_band.dart", "hash": "3f16406e2987428c29807c6ae57ba4ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "hash": "dd12f205ac5895a2bdf7d059cc4b83b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/datapager_theme.dart", "hash": "4e9144dce775176e6b91c004ce4779c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/samsung_galaxy_s20/screen.g.dart", "hash": "7cdf21c34b388bb2a448d7e84954f812"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_attributes.dart", "hash": "f1acbe1ea51585adf0a1ba560796bab1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/each_event.dart", "hash": "91b72e3a75068042bd3b16de99d2c990"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/matcher.dart", "hash": "e03e435d13626ba1dd09f8da7bf824a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zip_encoder.dart", "hash": "50f4059a7cc84a666cd0d6d0fa5256d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/generic/base/draw_extensions.dart", "hash": "b2998377740603791dd1762f0fbf686f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/range_area_series.dart", "hash": "4d638f8dc06900e18a17a9e8ff6ae831"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/fk.png", "hash": "0e9d14f59e2e858cd0e89bdaec88c2c6"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/pt.png", "hash": "b4cf39fbafb4930dec94f416e71fc232"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "d390b15ecef4289db88a4545e359bc8a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "e78589269f033237f43ffdc87adc47a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/deflate.dart", "hash": "103f40776bb12911b765159ea0db7aa8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/utils/shape_helper.dart", "hash": "0ffe1786d790b397af97c2264e2fa83e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/core/context.dart", "hash": "a07f8e10a45176e8210b1bbac38f3e1a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/standard_component_type.dart", "hash": "09973ba0a94d2d819052c0544dcdce70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/util/animation_controller_utils.dart", "hash": "a437e404371927d57b562ef3f95b37ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_object.dart", "hash": "0cb51131f14d4d8df95aee83e4931780"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "ecc072620f2a72e685360292690c8a68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ipropertystore.dart", "hash": "2e62c409a0c6ea9effbb7a045742e1b4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "6f516ffde1d36f8f5e8806e7811b15ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/imetadatadispenser.dart", "hash": "e653273473a891c0739e255d1b469d55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_match_rule.dart", "hash": "0298dac3221d4c6752b6207594e4f470"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_6.dart", "hash": "7eaf5b7e19afccfa1bde4bf16bf53648"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "e138cb83b907c09a4ac468dff69d43de"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "674ba42fbba2c018f6a1a5efd50ab83e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/fr_messages.dart", "hash": "95b84a32d1c9b959bcdc760bb13b83da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/device_preview.dart", "hash": "a52f7907cf8b238f041041f364f79862"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/tool_panel/sections/section.dart", "hash": "9dbfb976194d46fa2f4ff952cf39c52e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudioclient2.dart", "hash": "47b806a0c94783b8af1876a42cb6d0cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/newton_cradle/swivel_dot.dart", "hash": "198b1f6e50e6ebb72b22673c28f75c9c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/ru_messages.dart", "hash": "a5d96ba72526ccacf166fe370a14ea68"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix4.dart", "hash": "6250cc05770b9eca7a8010eaed7e5b94"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "75fa80ab7762b14e35b11b93da96d4a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl_helpers.dart", "hash": "fac5ee1098b41fef8637aca152781c92"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "b1884cfd8778cd71cea03ca8f4b39f4f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "95f488b1875988eb094e0ba71deb7deb"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "9645e1d88d63387bb98a35849f4cbe53"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ng.png", "hash": "15b7ad41c03c87b9f30c19d37f457817"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/misc/eof.dart", "hash": "6a083480a6cb878f98927a9271454bd0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mk.png", "hash": "8b17ec36efa149749b8d3fd59f55974b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/expression.dart", "hash": "1eea4f679ac434a2b413372c524c8d00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/title.dart", "hash": "0cef69b4b620bc5548a97e87b33e7eb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/indicators/stochastic_indicator.dart", "hash": "e2d8642909c08d695798213ec7921bf6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/info/info.dart", "hash": "9051447b8ef9ae835c458dce4d17c35d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/line_series.dart", "hash": "bbaed1c61cad0b2b990a582f6149d017"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sl.png", "hash": "a7785c2c81149afab11a5fa86ee0edae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/utilities/media_query_observer.dart", "hash": "e22ffd781f899c7b75c6da7c3bc56176"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/sphere.dart", "hash": "d1089412c69c2ca9e4eeb1607cf0e96e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/utf8.dart", "hash": "3b21907d68a2e99afa8e4103f6a72f78"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "7c07d5cc739ae29abcfbf6343ae84fdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "a309d8ca64c3efb3ad74b742ffb0e1dd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d33374c0857b9ee8927c22a5d269de9b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "04451542afc67a74282bd56d7ee454f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationelement8.dart", "hash": "2598a130fc6437cc87f8efb150561b60"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ad.png", "hash": "5be9e620c31e271a032ccaf984c4185f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "5f64d37da991459694bce5c39f474e5f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/lb.png", "hash": "b21c8d6f5dd33761983c073f217a0c4f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "0c46b12a4e0301a199ef98521f0ed3ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/treemap_theme.dart", "hash": "1f1a987070a588f08fbee8261eced769"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "6c0e97a3b04c9819fe935659014f92e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/misc/newline.dart", "hash": "bdd138e5e3c721f9272da59c10d7c5fa"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gg.png", "hash": "cdb11f97802d458cfa686f33459f0d4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "3e1bb909dcd21ccd8bdc03ba57bf02b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/node_list.dart", "hash": "4068e834e069179f5df23c7868664c19"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "501bafdb6d3784f18f395d40dfa73cd2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "db4a14227247e2524e46f6b0dd9da267"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/api_ms_win_core_comm_l1_1_2.g.dart", "hash": "7bb75bf1bcc0aac68c67c939cfe2eab0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ar.png", "hash": "bd71b7609d743ab9ecfb600e10ac7070"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "2c6facdb1b63e687304c4b2852f6ef4c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bf.png", "hash": "9b91173a8f8bb52b1eca2e97908f55dd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "bbc7eccdbd8472a2180e0dffce323bb9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_message.dart", "hash": "eb54a5ead5cb8ea548f36e4b8780e4b8"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "33ce088a133276cbfd4a33ec49bdcb62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/beat/beat.dart", "hash": "a7832c0385af57899b5760ec2221645b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationproxyfactory.dart", "hash": "7068099dc46731641110788c3b3e1bdc"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "2c525c85cb323db613ddc5eba4b902d4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "123520ee3a48eebf4ba444e93436bb1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/annotation.dart", "hash": "460d460ce32f43429d5ca098496bb19f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/inflate.dart", "hash": "2f1d6a5b171d0ded9b5984be4c426d4a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gm.png", "hash": "088268bc52242c3da421ccfe58b491b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.2.3/lib/src/method_channel/method_channel_permission_handler.dart", "hash": "219013e8bbe8cf19fde2d3520067ba39"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bt.png", "hash": "3c0fed3f67d5aa1132355ed76d2a14d0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ws.png", "hash": "8cef2c9761d3c8107145d038bf1417ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/et_messages.dart", "hash": "2ee9be812e5bee792113ca6bdbeae008"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter_localizations/lib/src/widgets_localizations.dart", "hash": "d509a11731c316d5cf31e5a220db0a68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/si.png", "hash": "922d047a95387277f84fdc246f0a8d11"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ng.png", "hash": "15b7ad41c03c87b9f30c19d37f457817"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ir.png", "hash": "df9b6d2134d1c5d4d3e676d9857eb675"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vibration_platform_interface-0.0.2/LICENSE", "hash": "6acb14e9d619a7b8e8d3b7ea12274470"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "53b9028402187f878713225b48bdd5bb"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gt.png", "hash": "df7a020c2f611bdcb3fa8cd2f581b12f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "72c318c3499a7a4d533965d32c6dface"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ienumvariant.dart", "hash": "ee434a4fa96c719b92f21bf8e27b42db"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "0e13760edcb9f90f659ba77c144a3461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/src/reassemble_handler.dart", "hash": "17dd5087a9b407563f662fc112624260"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sz.png", "hash": "5e45a755ac4b33df811f0fb76585270e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "5638f5f2028c522b32626825f6bd5b7e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.4/lib/src/messages_async.g.dart", "hash": "5598c63a098103e8c02942fc689fbb88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationitemcontainerpattern.dart", "hash": "85a9bfffa1576a9d933113d39528e24b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "cd7cadd0efa83f26d401a14e53964fd4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "2d3948bf5dd7b63d100270fce62fa2d9"}, {"path": "/Users/<USER>/Development/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "ebc6759fa73c53bc12d581d9e1e4c821"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mn.png", "hash": "02af8519f83d06a69068c4c0f6463c8a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/fm.png", "hash": "d4dffd237271ddd37f3bbde780a263bb"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/re.png", "hash": "6cd39fe5669a38f6e33bffc7b697bab2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/vi.png", "hash": "944281795d5daf17a273f394e51b8b79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/datagrid_theme.dart", "hash": "b253af9dff6e7dd37336da540fd6c52e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/bubble_series.dart", "hash": "51cda7f19389b00369ab67c827f3759d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/indicators/macd_indicator.dart", "hash": "0de86bcb915607419c28165309b9a6da"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "6486bc074c81ec57bdafc82e6a64683a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/sony_xperia_1_ii/screen.g.dart", "hash": "87c2110a6b4fa41220f47e8adba5302c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_m.dart", "hash": "167efb1e5d1b6fa8a22f6454fbf2a9c6"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/be.png", "hash": "498270989eaefce71c393075c6e154c8"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/nc.png", "hash": "a3ee8fc05db66f7ce64bce533441da7f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/kg.png", "hash": "a9b6a1b8fe03b8b617f30a28a1d61c12"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ke.png", "hash": "034164976de81ef96f47cfc6f708dde6"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "c06267b6c315a5e40f28feb6019de223"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/hm.png", "hash": "600835121397ea512cea1f3204278329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_literal.dart", "hash": "8388d5e13155ebde873438c26dc4ca33"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/pages/LoginPage2.dart", "hash": "5b2c53998c2b7cdf40d7b1b70d6847ff"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "43ba7557388f413902313df64e072389"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "hash": "bfc483b9f818def1209e4faf830541ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_visitor.dart", "hash": "61e938fe770ed7331e39f1dda1b64dd4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/lib/src/selection_dialog.dart", "hash": "b8a95961318fdbf1a7534f521e5e8633"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/lt.png", "hash": "e38382f3f7cb60cdccbf381cea594d2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "15059e9824dd4a9e06136d8dfd91c26a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "6f3424f2fc515abb888590b75c98e190"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "59b6b74779849bf5b836b84bb362b99b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/url_launcher_platform_interface.dart", "hash": "9190f2442b5cf3eee32ab93156e97fb1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/lu.png", "hash": "a05984f5f980e0f656456955b63b85b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/aw.png", "hash": "8966dbf74a9f3fd342b8d08768e134cc"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "2bc2f148be8fffe5f3a6a53fe8bc8333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib/src/fp16.dart", "hash": "63681ec4b0641faab6e783c1c213b5d8"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/tv.png", "hash": "493c543f07de75f222d8a76506c57989"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/definition/resolve.dart", "hash": "cbb8e1af9f1f0decfb6fc25a0725c51f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_breadcrumb-1.0.1/lib/src/breadcrumb_widget.dart", "hash": "c579a6bb130417cbf9f7be0f5b8d116a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iapplicationactivationmanager.dart", "hash": "88d299fd8892c37bab557a1ffb9cec20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/api_ms_win_core_apiquery_l2_1_0.g.dart", "hash": "153449b4c65c20e74489d7853e4ee4ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_g.dart", "hash": "edafd82e0b999bc51b79c8a3561ff1eb"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/so.png", "hash": "cfe6bb95bcd259a3cc41a09ee7ca568b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "ac317f8ed3b04bec644817e6f60a28d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/nl_messages.dart", "hash": "1d0cf1d8091b8ceeb53a29a6053b5d2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/inflate_buffer.dart", "hash": "b47889f85ed5635c5010e4a3cbab97af"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ck.png", "hash": "35c6c878d96485422e28461bb46e7d9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ishelllinkdual.dart", "hash": "2e8ac7faef1638b9d8022b3da82c3588"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/samsung_galaxy_note20_ultra/device.dart", "hash": "41f1877a2c9f708a4af6803f51f89041"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "6f18c18a1a5649f27b6e0c29dfba4dc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/locales/default_locales.dart", "hash": "3831b1457a1a0464a32db8c99117a5a1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "955794ab8f9f2f33f660998c73ac222f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/auth_messages.dart", "hash": "f36296a5403e8c6265b437c034411f50"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "c8564aa311746f4047cd02e26ff4df75"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ky.png", "hash": "666d01aa03ecdf6b96202cdf6b08b732"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/indicators/technical_indicator.dart", "hash": "a5da91c2eb8ff3be4cb83ab48dd2ac8c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE", "hash": "612951585458204d3e3aa22ecf313e49"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ax.png", "hash": "ffffd1de8a677dc02a47eb8f0e98d9ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationboolcondition.dart", "hash": "96cd038c21e3727eb6325be0268a7ed6"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gs.png", "hash": "524d0f00ee874af0cdf3c00f49fa17ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/bthprops.g.dart", "hash": "791b58d08f6e26165658bbd5ad0c5b2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/sheet/cell_index.dart", "hash": "3ccd264d4cd2bd0991c28cea1203293c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/bzip2/bz2_bit_reader.dart", "hash": "7a47625dd33a9ef0a154e45380951aaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "89ae530b1eb1ce798ec54bc9b45efdba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/map.dart", "hash": "822f0a79dfd6a3c997d2b898ec420b97"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "b24af65afbe06cb00d5661df3d3083af"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gd.png", "hash": "42ad178232488665870457dd53e2b037"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_4.dart", "hash": "54d72b1c5b9977ccdcb6cd95e8acc7e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/core/result.dart", "hash": "fbca1545a7230f0ea39d7884a1722475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib/src/bloc_overrides.dart", "hash": "79a59aed387a11d74fc095969e8cb25f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/lib/src/messages.g.dart", "hash": "c9f86799b4e2663cc3b8ee539b87c07f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/utils/utils.dart", "hash": "a873f9930f6849a6c54e36c7b3d9d69d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "737365e0b93f911e49f1ac1e5363564c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "951bd729c13e8dd03a7f4edd8b10c06d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "9ad11b4bdb179abe4ccb587eb0e2aebc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib/bloc.dart", "hash": "b7ddbd596df9c1984fc4f2d3607d4ffc"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/material.dart", "hash": "ff1b06a4c51e36902ef2e5cf96495fea"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ss.png", "hash": "842472f6dfe9dc2e01d7efd4a9877d41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/shell32.g.dart", "hash": "77833f9ce93791f664316db43a55505c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/version.g.dart", "hash": "b967c8105d10206324262df9fb1a662b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cm.png", "hash": "89f02c01702cb245938f3d62db24f75d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib/device_info_plus_platform_interface.dart", "hash": "4b532e7a43e7a2a180a49c5869ec8ab4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sx.png", "hash": "8fce7986b531ff8936540ad1155a5df5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector4.dart", "hash": "7d33539b36e15268e2f05b15a9f5e887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationelement.dart", "hash": "ce305fb96ca9a74ff549e6ff91795e10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/generic/desktop_monitor/frame.dart", "hash": "11fd70d996ff8539ce59a0ac109ad708"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bv.png", "hash": "f7f33a43528edcdbbe5f669b538bee2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ipersistfile.dart", "hash": "d27d71d2351cdb9c560055671b5ad215"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/pr.png", "hash": "ac1c4bcef3da2034e1668ab1e95ae82d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/model/chartModels.dart", "hash": "8301baa1bbcae8e0e942628bad2999e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/registry_value_type.dart", "hash": "d2abc4bf73a6fd6720bf0bc3b1a461f2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "245a31a30063b63cbfd631fdc2ddf0d8"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/zm.png", "hash": "29b67848f5e3864213c84ccf108108ea"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/utils/excel_helpers.dart", "hash": "d74d3352cceb01cb5a177560cfaa6118"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/to.png", "hash": "a93fdd2ace7777e70528936a135f1610"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ph.png", "hash": "de75e3931c41ae8b9cae8823a9500ca7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/connectivity_plus_platform_interface.dart", "hash": "88d5feb6f0a1ddf0cafe75a071bbcab2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_introspect.dart", "hash": "1d43aa18b7cd09879287a4e8ba5ea5ef"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "56a764067b45a1a7cb6b7f186f54e43a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "72bbc3da5da130fb11bb5fc65614653c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document_fragment.dart", "hash": "88acdeb4b5b5a9e5b057f7696935fc2e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/widgets.dart", "hash": "9f9b1fcdf4037b3b4c71ed65b57e87f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screenshot-3.0.0/lib/src/platform_specific/file_manager/file_manager.dart", "hash": "b68877398e702e035fbd444a41451d75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_platform_interface-1.0.3/lib/src/method_channel/method_channel_open_file.dart", "hash": "dad82b26d6477c1f40fa8d0012a18929"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_se/device.dart", "hash": "4d7d84023ea82e009755c65d47a71478"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ishelllinkdatalist.dart", "hash": "68642e049d1aa7d3e55fc1382b0696c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_async_platform_interface.dart", "hash": "03664e80d73ff10d5787d9a828c87313"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/pages/DashboardPage2.dart", "hash": "49cd59e97c202575a5c1af7b8d6376b1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/af.png", "hash": "44bc280cbce3feb6ad13094636033999"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/guid.dart", "hash": "e5a79b510256712e5dbab68965722534"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/getuid.dart", "hash": "49d6d829ae481b2570a290401389d149"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/progressive_dots/progressive_dots.dart", "hash": "151fc26672caa64d5145efb7e0084877"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ru.png", "hash": "9a3b50fcf2f7ae2c33aa48b91ab6cd85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/digit.dart", "hash": "ea08cf8b71713e3535d5a2164a8bc7e1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/lk.png", "hash": "56412c68b1d952486f2da6c1318adaf2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/images/red_calibrate.png", "hash": "dd214b973df304ebac14e7f8aafd369c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/_crc64_io.dart", "hash": "f6e82da324f67628362bc63ed3314081"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/tg.png", "hash": "82dabd3a1a4900ae4866a4da65f373e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/entity_mapping.dart", "hash": "5abb58e10e8ea85ea5990a97ee20ae4e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "ddf1bde8f4b9706d5769690b7819e5d4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/pn.png", "hash": "ffa91e8a1df1eac6b36d737aa76d701b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/doughnut_series.dart", "hash": "19d92380ca39c8f29326e88fd4d80be7"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "32b222420709e8e40d12f6ea9fc0041e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "964f3ee4853c34a4695db0c7e063eaa3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/theme.dart", "hash": "88718f823c0de3f8398075d24b150ecf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/encryption.dart", "hash": "5a53fc34e740ca261be6ebb21b62268a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cf.png", "hash": "625ad124ba8147122ee198ae5b9f061e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/it_messages.dart", "hash": "ddd22e86401c5255a26bd35eed0820ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/src/proxy_provider.dart", "hash": "57b51f6f00c6bc3a29abbf83fbd804f8"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/tf.png", "hash": "dc3f8c0d9127aa82cbd45b8861a67bf5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/slider_theme.dart", "hash": "0d1bd1111caadd8aec89b608bdea4961"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "a79a6f9bb06c7d6dc5fb74ac53dce31b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sr.png", "hash": "e5719b1a8ded4e5230f6bac3efc74a90"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "31b0d2bf647a0ce615f4937dd5307b1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/lib/src/exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/advapi32.g.dart", "hash": "e88da16e1dcd78ac58401bf0c3134c89"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "62cbf59e5c816c224ef5eaf803fc877b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/src/generated/top_level.dart", "hash": "3418e2ba1365bf8820838eae0da072fc"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "14177be7a74b321668af2b9effa0f396"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "c2dcf2bcdc85d007f9729621d13cccf4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/core/parser.dart", "hash": "650a69386b74cc44539e06ec0e7fa080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/geometry/vertices.dart", "hash": "5db8a9ae89b9f40979a35f8c0eb56638"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cf.png", "hash": "625ad124ba8147122ee198ae5b9f061e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_linux-0.0.5/LICENSE", "hash": "54ca5f4345f518f70f047f1f2e585945"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "27a4ea7d50fcfd776a5d69fce0cd26ad"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sk.png", "hash": "0f8da623c8f140ac2b5a61234dd3e7cd"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/pf.png", "hash": "3ba7f48f96a7189f9511a7f77ea0a7a4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/lu.png", "hash": "a05984f5f980e0f656456955b63b85b3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "0fa4800227413041d2699ed47918c7f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.24/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "eb9b3bf513b18ddaf0057f3877439d9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/default_method_channel_platform.dart", "hash": "c7c117f8683bd3335fc56fb4e706d271"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/chunked_coding/charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "530c4f96f1475cc4e4128ffedd705028"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sl.png", "hash": "a7785c2c81149afab11a5fa86ee0edae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/pattern.dart", "hash": "2108c716fd8198fa3a319a1ec6cadc9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_l.dart", "hash": "90a6d35e7a7db7adff31af7c8aeb6182"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/drawers/profile/authenticator.dart", "hash": "10d6cc3f638b724098412de1cb4e98e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/xk.png", "hash": "b75ba9ad218b109fca4ef1f3030936f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/text_direction.dart", "hash": "45f61fb164130d22fda19cf94978853d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/uppercase.dart", "hash": "7061b91c27425c907020fe54e569b9db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/propsys.g.dart", "hash": "769b47b5febf91e7831fd0040b4d3ed0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/byte_order.dart", "hash": "a13e91c8cc53cc5cf91fc5ec7877f9fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_13_pro_max/screen.g.dart", "hash": "f7792b39ca8292bc1cd78b3bb497ec18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuri.dart", "hash": "7531be50f5bc7d9a762e8842525fc199"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mp.png", "hash": "60b14b06d1ce23761767b73d54ef613a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/cursor/cursor_painter.dart", "hash": "704f101cde0eb584888ea4e511193b79"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/billing.svg", "hash": "b3ff1525b8f077f37b8c55939eeefaf4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/a4.svg", "hash": "79b0fcb5a4a0400002c17611c2f95ab3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "84e117adf104c68b0d8d94031212b328"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/samsung_galaxy_a50/screen.g.dart", "hash": "782b3893ef39361010f49a5f17eb7b73"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "0201ee9c8aee2bb24db2c74b6c0cd485"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/definition/parser.dart", "hash": "7aac958977c79edf01e6ad44a726b52b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/kz.png", "hash": "cfce5cd7842ef8091b7c25b23c3bb069"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib/method_channel/method_channel_device_info.dart", "hash": "811012c24b5b0bca98b118bdbffbfccb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/dbghelp.g.dart", "hash": "0eab209847ef951bd0a6ff1418f74ba1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/al.png", "hash": "af06d6e1028d16ec472d94e9bf04d593"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_remote_object_manager.dart", "hash": "f2f859e737366d0ff7feefaf0c41fa85"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "4d8781c671b7df5aadf2331931458cfb"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ly.png", "hash": "561134dc2f91764ed93227639cc7f599"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/tool_panel/widgets/device_type_icon.dart", "hash": "3331892b9eb0ff5517e72bae5c9fce87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/matches/matches_iterable.dart", "hash": "037df9e7342fc8b812d985c8b6e8a0c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/visitor.dart", "hash": "a109d3c954a67fad39c6808178d6eece"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/shlwapi.g.dart", "hash": "bd016bc06a43b71c304daef7333df5cf"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bv.png", "hash": "f7f33a43528edcdbbe5f669b538bee2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_12_pro_max/screen.g.dart", "hash": "234f799ee6e674c133379249341e217d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationproxyfactoryentry.dart", "hash": "34f1383424d8e23bc3463188bcf19dcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/shared/annotations.dart", "hash": "9a469ff3de60c96cf2f9b0523b651782"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "03d585dfc6055d74a4668e69263afa5a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/md.png", "hash": "7b273f5526b88ed0d632fd0fd8be63be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/linux_device_info.dart", "hash": "2989c1b0519c699587a5e31d87597fca"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/pg.png", "hash": "06961c2b216061b0e40cb4221abc2bff"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/vn.png", "hash": "7c8f8457485f14482dcab4042e432e87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/range_selector_theme.dart", "hash": "e92bd0d48ab1e61b946f25ce4f0bdda5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_descriptor.dart", "hash": "df2373fa53c57974996330429774683f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/hu_messages.dart", "hash": "9601062a07b964b48ea83cc9ede16205"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/isensordatareport.dart", "hash": "50a6a93f5f53543a005e436586f9e24b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "d953dedc9eee14dfb343f4c5988840c4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "912b76b3e4d1ccf340ee3d2e911dfd28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/isequentialstream.dart", "hash": "b59195eae40d21212bb7b532313e6480"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/nr.png", "hash": "9f15185ce7927f35c7610230cee0b0fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_decoder.dart", "hash": "d16fc08d820f892bacb508cc3e45935e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "2baf11d03f1f50ccef5294c1fe810e25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/samsung_galaxy_note20/frame.g.dart", "hash": "5f02b3b2f4976e0352a664ea6eec9cdd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/node.dart", "hash": "9ec244272cb6c8da46a6dd5f104f0dfe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/utils/helper.dart", "hash": "308e5703b50b31ff577eee426aadc024"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gb-sct.png", "hash": "075bb357733327ec4115ab5cbba792ac"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gp.png", "hash": "6cd39fe5669a38f6e33bffc7b697bab2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/feedback.dart", "hash": "c8f69577793923bfda707dcbb48a08b1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gb-nir.png", "hash": "fc5305efe4f16b63fb507606789cc916"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cv.png", "hash": "60d75c9d0e0cd186bb1b70375c797a0c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/nu.png", "hash": "c8bb4da14b8ffb703036b1bae542616d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ishellitemfilter.dart", "hash": "3fb5dd9d7f42a9e619dd81d5bbead392"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationscrollitempattern.dart", "hash": "b09f09d05be41a57a141f88709700efd"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/lib/country_code_picker.dart", "hash": "55d776901722a51eefdc170e8cb6d443"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtransformpattern2.dart", "hash": "83ddbf5c126feed94b2f90784c17d5b1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/noentries.svg", "hash": "d4291fa0756899ea5c38233941a05d25"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "ffc66c213d3e015ff3e03298622c7141"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/irestrictederrorinfo.dart", "hash": "6bca90e19560bd62e32b8e41c835d71d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "eb115c2e8f0ff170bf26a44efd1b5c05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudiosessionmanager2.dart", "hash": "0aea2ad4289b60950d9a467b0e03e80e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/hu.png", "hash": "46b4e675795ba7d526187e1f97841fca"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "4895dd7c08da98c883cb21943f4ca4d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/timeago.dart", "hash": "7502f43fb1cb67fd53164c69bbb3b58e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/settable.dart", "hash": "71104e51eaff06217afc51575bb2848f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/core_tooltip.dart", "hash": "f1353e500998a1e2924c050db220f10e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/date_format_internal.dart", "hash": "46f06f2d32f61a3ebc7393f1ae97df27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iwbemcontext.dart", "hash": "659397ba2b8ba2809c7855a21f2f60b2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "ae85856265742b6237ed0cb67c4364af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/irunningobjecttable.dart", "hash": "03e32ac40b7907db555eec5ac3a5dab5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomation3.dart", "hash": "c5d7abe9da153df1f3d9d7754b91c0fb"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sd.png", "hash": "93e252f26bead630c0a0870de5a88f14"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cy.png", "hash": "9a3518f15815fa1705f1d7ca18907748"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/indicators/tma_indicator.dart", "hash": "26d3e7330f67b324f074f0f03a0a1b5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/lr.png", "hash": "1c159507670497f25537ad6f6d64f88d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/web_helper/client_save_excel.dart", "hash": "80ffa054dc024efd869d094d7819826b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/sheet/border_style.dart", "hash": "541f1ab77f9f15ad62b8ae156b1cde00"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "8383986e94be1a258a59af29b9217876"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/images/1fqC.gif", "hash": "63317f508ac8d60e972b37c4e3ba150f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gb-eng.png", "hash": "0b05e615c5a3feee707a4d72009cd767"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "177fda15fc10ed4219e7a5573576cd96"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "547eac441130505674f44bf786aee606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/cs_messages.dart", "hash": "d6f8ffdb18a778270cedff4dfdc41188"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/api_ms_win_core_winrt_l1_1_0.g.dart", "hash": "41bc035ab11c30618d860e3d24e2c4ef"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "982099e580d09c961e693c63803f768d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iwbemlocator.dart", "hash": "0183b13f6e13fe4c255b09236e142882"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sy.png", "hash": "2e33ad23bffc935e4a06128bc5519a2b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/a2.svg", "hash": "1b24c777f385037b1767ee85a330aeb1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "0f717ff4ecfdaa0347894abbedd5d1e9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "490fffadb78eb29c5fe209be7fe12370"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/arena.dart", "hash": "b9bf4c34257ba7cad08d8c7092c46e35"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "5486e2ea9b0b005e5d5295e6c41ad3c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/three_rotating_dots/three_rotating_dots.dart", "hash": "7a57e9fe51b2dd34d7fe0e61a8133a4d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "a88d8ea7c8c98dd1d35ad2853f04efe1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "f1c0b135f35af022771e30409953e0f6"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/view_model/data_post_requests.dart", "hash": "8ce2e00707d09fb9a141a11c22f3423c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/state/state.g.dart", "hash": "72ecc3475f66efdc486ff08f4de2a3ab"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/trends.svg", "hash": "3febc86f0efbd5680701d5e8d263b382"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/stacked_bar_series.dart", "hash": "d49d32bc02aa7fa6557bdf9354f2077f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/axis/axis.dart", "hash": "343d8b595700d8fe254e2399ed7a96d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/cast_list.dart", "hash": "87751ee02d315bd2d0c615bbf2803a3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/src/enums.dart", "hash": "1c71712af9ddaeb93ab542740d6235fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/misc/position.dart", "hash": "faedea5895c9ddd2b2c270817c61d1f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "f996ce49eab57718350b84e11ea3192d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/generic/phone/device.dart", "hash": "23f3a773f5200145fe941380887032b8"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/om.png", "hash": "79a867771bd9447d372d5df5ec966b36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.15/lib/src/loader.dart", "hash": "14d64729800391fb58a32643b7126984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/style/obscure.dart", "hash": "7d65086dc66afcd85e85fee3293db274"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "796af05466fbe319d5fc699b982ded0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/bn_messages.dart", "hash": "60dcf31dc996c50a1ad71e812cb92d95"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "ca3df05f249dbc5a38ebb86ee9a74a1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/macros.dart", "hash": "61161beafb5147bd9216c091fbe776c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/combase.dart", "hash": "10ffd776cef80532a293c5673825e655"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "5b66a624b831dd9a7c94d59aaa10f8bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/node_type.dart", "hash": "57e5dc91c30bff1774eaaa45a798d0df"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bb.png", "hash": "a5bb4503d41e97c08b2d4a9dd934fa30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.17/lib/src/default_theme.dart", "hash": "b310006c8257038eceb57e051139592b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "ccb3c80f13485133893f760c837c8b62"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "1303bc77ad63625069f2d23afc73f523"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bz.png", "hash": "e95df47896e2a25df726c1dccf8af9ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/lookupmessages.dart", "hash": "d61223112b24cee380b1ba0010a81279"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/circular_data_label_helper.dart", "hash": "4a5d7d531de6ba044dab1f797b16acba"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "12120b49ba363d4c964cf1d043a0aa1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screenshot-3.0.0/lib/screenshot.dart", "hash": "6503b1c6ae9108d3bcba4691619b54af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_r.dart", "hash": "c03845abf8fa02fedbc602853685d92a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/archive.dart", "hash": "c26dc668c2a5e8051394f89587bdc3de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/tar_decoder.dart", "hash": "9d35e65a23a3fd6bcb0a545ee833f8c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/annotator.dart", "hash": "2678ef31818710bda6610b84fc25d915"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/funnel_series.dart", "hash": "854fa2a58c61053344f27d1d6f4e5dae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_signal.dart", "hash": "8596b58c127792783625b4b22a4d023c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "438f80a3d5361329aa6113e3409440aa"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "becd419f96efe14f36f18a8c8adc82cd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "d3de616e525e730c7b7e3beb57930993"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ph.png", "hash": "de75e3931c41ae8b9cae8823a9500ca7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/ntdll.g.dart", "hash": "80549b960bc9c7fd9dad05aa69b7d9b2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter_localizations/lib/src/utils/date_localizations.dart", "hash": "eab3afdf13cebd3927cc12a7a8c092e2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/table/DataGridWidget.dart", "hash": "93f567ce06940beacebd530306ffd0df"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "ec0bf24485bc5f9b825a382457f586e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/bouncing_ball/bouncing_ball.dart", "hash": "cbb4cc6023a382574c923e72b9e7f01a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/text.dart", "hash": "d3de5e8090ec30687a667fdb5e01f923"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ml.png", "hash": "1a3a39e5c9f2fdccfb6189a117d04f72"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "11fc97acd20679368ae2eaa698c6f130"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/flutter_bloc.dart", "hash": "f147477b0996859cb36b27c7f425482f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/iq.png", "hash": "dc9f3e8ab93b20c33f4a4852c162dc1e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bw.png", "hash": "04fa1f47fc150e7e10938a2f497795be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_12_pro_max/device.dart", "hash": "d480749fad529ab62a5c0c94e4ced65b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "73089c9737db54a05691e09bc9fc1bcd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "c86a43bc5abf7528416982490b4c0b8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudioclock2.dart", "hash": "36e63388665f9d5f335135824e300cae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "b656f459fa4dd04f817455858d3dd20f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_12_mini/device.dart", "hash": "8d50c4a1ab307c3dd2edbe7a7d70bf97"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "bf50f61746b9744a0e2d45a88815288f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/pdfviewer_theme.dart", "hash": "f0fc26407f8647ec9f704bf08fd09153"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/find.dart", "hash": "9f4ec4b8e28d5bf94cbd385aa48eb91f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestapplicationsenumerator.dart", "hash": "8d4c4f339184d3cd86b0dfb7d7321d51"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "0119e0f7758ee8ef19baeae2b96cb389"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "c0cf85f80b79542d2b0e1a00547d7310"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "ed28f6ca17f72062078193cc8053f1bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/file_io_desktop_and_mobile.dart", "hash": "a2f208880d92532a9d975bee2451eee6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.3/lib/options/apple_options.dart", "hash": "d4efda9ec695d776e6e7e0c6e33b6a4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/zlib_decoder_base.dart", "hash": "1843b987225d300957fc26c92d434833"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/an.png", "hash": "469f91bffae95b6ad7c299ac800ee19d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/nz.png", "hash": "b48a5e047a5868e59c2abcbd8387082d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/with_parent.dart", "hash": "cff1275e3376c28419f9b54215ec8b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/ja_messages.dart", "hash": "56e33d5904229ddbc511e22bd60ca93d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/scarddlg.g.dart", "hash": "a40d6cf5dd5de2536012a2ab2690e67e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomation5.dart", "hash": "3681275c274b0e2b2c9dc14ecc273c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.1/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "a8fdf31698b305c9fdad63aa7a990766"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ienumnetworks.dart", "hash": "c07567abbc3cd64d4f3175c3e142da55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/lookup.dart", "hash": "22d4076f2d38c3a2fed532fb53ecb1a3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "25b96e83b1368abc11d4aeae19e9f398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/stacked_area_series.dart", "hash": "d0b2bf87567d520f9028ca122c4292fc"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "f6816ebd27db772616d01f543b33d0f8"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "b9bfa2dc31960df2f1fd3aee88c3807e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "c9111e47389ee4b70aab720435a2a2df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestreader6.dart", "hash": "693ddae25fe758b1b3329d7d0ed5a005"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "bbc9542eb5e3c4701c24bc1268b8165c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bs.png", "hash": "814a9a20dd15d78f555e8029795821f3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "8f24c8ed1935c6f08997d0b9acb5bf38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/samsung_galaxy_s20/frame.g.dart", "hash": "38fc799e0366adc853532fd81ea6f1ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/LICENSE", "hash": "733ee64399ecdc51b3ab17c423271ccb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ienumstring.dart", "hash": "e7c7233769f55e718ce22082f70db721"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/view_model/custom_exception.dart", "hash": "157b2f92ed9e0fec5fbe3a54443d5d8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.15/lib/src/vector_graphics.dart", "hash": "cd2568b89547fabd11d9d6b81a3136e1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ca.png", "hash": "bc87852952310960507a2d2e590c92bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/src/selector.dart", "hash": "6a72a2ba15880cab1e1d9a28a94f1a2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_buffer.dart", "hash": "99760254cc7c1941d4d7d7bb0fad045d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/extensions/unpack_utf16.dart", "hash": "cfab296797450689ec04e7984e7d80e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/widget/pin_widget.dart", "hash": "82a1402e09e03752e09c6aed9dd28b1c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "5ac341d21fd38e1a3307100a5b3c3138"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/matches.dart", "hash": "e2bb1be234c319b0c09b51cd14f9ab51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/LICENSE", "hash": "66cd5ac1a024a6473da4f5bff0b39f1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iwbemhiperfenum.dart", "hash": "2b344fedd3805594c1c2981f8c06f148"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/za.png", "hash": "aa749828e6cf1a3393e0d5c9ab088af0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ms.png", "hash": "32daa6ee99335b73cb3c7519cfd14a61"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "1dab3723527db6a19410ed34b6acaeed"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/us.png", "hash": "b1cb710eb57a54bc3eea8e4fba79b2c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/staggered_dots_wave/staggered_dots_wave.dart", "hash": "8efac8d54dde00ca623c3955b2730ad0"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "c3ccb5b6cd3df44e6587a4f04dd6a4e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestpackageid.dart", "hash": "74afb02c8643f153de3fb64ad8a466a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector2.dart", "hash": "6b519d909b25ca9d144af7972d689c6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/util.dart", "hash": "d66f7ff750a1747331f6a8eff5de618f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_error_name.dart", "hash": "7398500b1824f6043f23e208cd993866"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "f663757bacdc28f2692b30a293d75146"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cw.png", "hash": "db36ed08bfafe9c5d0d02332597676ca"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ch.png", "hash": "7690a93c44b5212b11c7e49b218fb2cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/storage/file/file.dart", "hash": "c528fe5789668bd6137bd1e1bd4d3274"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "5da306e7f2542e5fb61efff6b4824912"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ml.png", "hash": "1a3a39e5c9f2fdccfb6189a117d04f72"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudioclient.dart", "hash": "e3cf86a21b6646a68ce37d952b5ecf5c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/br.png", "hash": "8fa9d6f8a64981d554e48f125c59c924"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "b269f9d6378b540b7d581db466ad98d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/pattern.dart", "hash": "cd6d0f606129e54552c5fee950e32bfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/event.dart", "hash": "1a7fe7a35dbd168a7f2e10065f4a3158"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_2.dart", "hash": "dc92a928880163bbe0232a641f7f4276"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib/src/path_parsing.dart", "hash": "ce49b6015a5f5b5bb716420efbef22c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/imoniker.dart", "hash": "e6febe06d728a39b4945898e0b1294d5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ua.png", "hash": "dbd97cfa852ffc84bfdf98bc2a2c3789"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/a5.svg", "hash": "934762f95d197e371e6eaa6d9f3a19aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/zlib_decoder_stub.dart", "hash": "d703348c150f2f9529f252667d77ee40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/decoration/decoration_boxtight.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "e45c87e4aadaebf7ba449f4c60929928"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/vi.png", "hash": "944281795d5daf17a273f394e51b8b79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/utilities/fast_list.dart", "hash": "dac0da827c11cd613e8692614f29bbdd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "cad4582fa75bf25d887c787f8bb92d04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ps.png", "hash": "b6e1bd808cf8e5e3cd2b23e9cf98d12e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/by.png", "hash": "caec57cd103f0c35d90946540e69e3de"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/dj.png", "hash": "dc144d9502e4edb3e392d67965f7583e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "2aec07fe4a1cd25aa500e5e22f365800"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/au.png", "hash": "600835121397ea512cea1f3204278329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_13/device.dart", "hash": "285c5d9d9bcec15468eafd4fa12c3282"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "3f9362642d37e0d97860181e8a1dd598"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/nn_no_messages.dart", "hash": "d1e77eca0beba7d2e73139ec28373781"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/url_launcher_macos.dart", "hash": "ff296a17d3582fcd8fe99bfb544a3978"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/interactions/tooltip.dart", "hash": "290a392181952513818bab1c35087e97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/builder.dart", "hash": "3131838b519fd1bcdc3486eb44d640d8"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "c5e44030289c2c25b26c5b3aa843b3cc"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/uz.png", "hash": "d3713ea19c37aaf94975c3354edd7bb7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/histogram_series.dart", "hash": "eebb8770778ef7706a0adac6b83e9217"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/tk.png", "hash": "87e390b384b39af41afd489e42b03e07"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "7b0e6dd1794be4b575ecf8af6475f0e7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/er.png", "hash": "8c4feba235695208189a3ba0632ccd08"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/.dart_tool/package_config_subset", "hash": "f166d0514add1c9639200f7df994f077"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/inetwork.dart", "hash": "d2bb1791822e1c17a18ea8f306180296"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "fddd73db94bb2fa3a0974bed845f32a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/LICENSE", "hash": "06d63878dac3459c0e43db2695de6807"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ai.png", "hash": "cfb0f715fc17e9d7c8662987fbe30867"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "d3b949a1e7578291493af5fd28846314"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/eu.png", "hash": "b32e3d089331f019b61399a1df5a763a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/zh_messages.dart", "hash": "21ea6c0ebefeb7f4272a4f357ae72273"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "6ea409faabc2d30760053a8936e45796"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "53cf0d76bfd70bfdc7e2edb4a18327f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.2/lib/local_auth_darwin.dart", "hash": "2d0b26be5c4a3422283198cf7c138dbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/newton_cradle/newton_cradle.dart", "hash": "51e0744ff48f80e5fc0f41de18e18cfa"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "940daf4491e3ab2e15d7eac5d6ce6b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/utils.dart", "hash": "ee746523b6e3d70f4061a5750c37cac3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iwebauthenticationcoremanagerinterop.dart", "hash": "da6fd295116b361d1a0258580d3db629"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/images/error.png", "hash": "d5ee96ce1fff0eca4f1f4cb22f9fa112"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ienummoniker.dart", "hash": "d80a4e0d1a5fe4aba72f8df70e8b660d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/isensormanager.dart", "hash": "bf3a7e591cc9c80a09c1843209bdafdf"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/material.dart", "hash": "f485bc1aa4fbdf87e17bfb8f80e39258"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/af.png", "hash": "44bc280cbce3feb6ad13094636033999"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/geometry/path.dart", "hash": "6aad1022a507304fcaf64ce514638a17"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bq.png", "hash": "67f4705e96d15041566913d30b00b127"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/event_codec.dart", "hash": "16d220671ba632751edb02e31809a2a1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/tf.png", "hash": "dc3f8c0d9127aa82cbd45b8861a67bf5"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "893548eaf87a8fd903da6fa761ad5ec1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sm.png", "hash": "b41d5b7eb3679c2e477fbd25f5ee9e7d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mf.png", "hash": "6cd39fe5669a38f6e33bffc7b697bab2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "cb45dd3f32378f0acf6b8a514cdc6084"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/three_arched_circle/three_arched_circle.dart", "hash": "4f316d0c1eb52098f3e488be468c34bb"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/nf.png", "hash": "5e12fe669c82c295812e27e9f1e410c7"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "13be7153ef162d162d922f19eb99f341"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "89aeee125822690cbd46b2ff43c76ec1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "dd3402d5403be91584a0203364565b1b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/table/fields/ChamferedTextWidget.dart", "hash": "39fe66c81ca8565386960c21acbed9aa"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/sliver_floating_header.dart", "hash": "5ffb77551727a0b5c646196e7bf1e9bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/zm.png", "hash": "29b67848f5e3864213c84ccf108108ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/range_slider_theme.dart", "hash": "37f734fd8921420a243e4085aa71086e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/generic/tablet/device.dart", "hash": "cd00261b79a117515588f24662b76615"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/lib/src/line_scanner.dart", "hash": "2ef7a4661f5de3b48852c8a0e0fe36aa"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/dropdown_button2/lib/src/utils.dart", "hash": "b06b0c0ef83ffebc76d489622a8fd409"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "0821fcdff89c96a505e2d37cf1b52686"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ba.png", "hash": "9faf88de03becfcd39b6231e79e51c2e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/app_icon.png", "hash": "66a247274637120847556fbf1d6ae725"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "3cd5a71cfa881a4d3d6325d6b2c6d902"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "ec2260a55dbb3ff283297d9da97e130c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtextrange3.dart", "hash": "1b5fd1f26a29d303d480169a8310b991"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "5c96449c2a494ea8f3a50ecc3ba9af74"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mz.png", "hash": "40a78c6fa368aed11b3d483cdd6973a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/geometry/image.dart", "hash": "abebf3289b53959c8e9375645eb33659"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/mem_ptr.dart", "hash": "ef30f30878ef0ee91ca912d2adec344d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bo.png", "hash": "92c247480f38f66397df4eb1f8ff0a68"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "61af6ead2e2dc04677bcfb8c0c2104ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.3.1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/pa.png", "hash": "49d53d64564555ea5976c20ea9365ea6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/calendar_theme.dart", "hash": "6150246a598f1067ce70908e55d88929"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/yt.png", "hash": "6cd39fe5669a38f6e33bffc7b697bab2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "c9105f08cb965dfc79cdbe39f062d6c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationproxyfactorymapping.dart", "hash": "ce859dde3195c55b2efccee1bdc51a60"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_i.dart", "hash": "706f1120f2aad4e908056a2b4f16eb23"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/wf.png", "hash": "4d33c71f87a33e47a0e466191c4eb3db"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ir.png", "hash": "df9b6d2134d1c5d4d3e676d9857eb675"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sb.png", "hash": "e3a6704b7ba2621480d7074a6e359b03"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "bce1e8ef07d9830bbf99031d77e0b9fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationpropertycondition.dart", "hash": "82e0e5b8ffeefc064a87f7990e0585b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iconnectionpointcontainer.dart", "hash": "83f156972f99a181b244f428cdf134bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/accept.dart", "hash": "740f17823564c3c7eca15bca5c110e17"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/drawers/profile/activeDevices.dart", "hash": "d9643916790fc25eddcc27d18e114d73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iwbemrefresher.dart", "hash": "54ba07d769f852b6c68fa2aafd4257c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/device_info_plus.dart", "hash": "33970ebf975bcd8cde1fa7156460e38d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/localizations/global_localizations.dart", "hash": "358416b83855424a3433e2cf6a730c43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ishellservice.dart", "hash": "b7690366684d9173683d36992173f7a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/unbounded.dart", "hash": "a617a91b12a3156406da1d95552aa4a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestreader5.dart", "hash": "a938094da69cf329b021d7351a0860fa"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "b139a58dace0b9d9a07a3523ed72ced5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/dropdown_button2/lib/dropdown_button2.dart", "hash": "5e53735d3e2f36928f33e377eb5782bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.3/lib/options/ios_options.dart", "hash": "704d7f872888ec6e9697123a180fd95d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/isensorcollection.dart", "hash": "b43a69dd26a10426aeb7eed269b4cd51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sm.png", "hash": "b41d5b7eb3679c2e477fbd25f5ee9e7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ifiledialogcustomize.dart", "hash": "2815892e3735c223c62476ddaf4cb27f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/sheet/font_family.dart", "hash": "7692662e372d4dce2ab1d8818867bf6e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/core/exception.dart", "hash": "5934d7c0ee1ff94ec21aad560e9ed8ba"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/jo.png", "hash": "d5bfa96801b7ed670ad1be55a7bd94ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/url_launcher_platform.dart", "hash": "0321281951240b7522f9b85dc24cb938"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/xk.png", "hash": "b75ba9ad218b109fca4ef1f3030936f1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "056355e344c26558a3591f2f8574e4e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.g.dart", "hash": "7ff35a1db7f2b80a156d464b075a09f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/flutter_screenutil.dart", "hash": "15f1990c0ac3146200fcbdcff0779a7c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/containers/CustomMonthDropDown.dart", "hash": "adfabc2d83aac9b8f099a95a6cd85baa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/info/device_type.dart", "hash": "dabad368d59f444dce228a3c2919abdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/archive.dart", "hash": "7008ef5f2a7d0e38ee2fbaa51a89ef24"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ge.png", "hash": "93d6c82e9dc8440b706589d086be2c1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/src/bloc_selector.dart", "hash": "af53bded9c174a962090a3850b3698e6"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "e4a748e0ab7265def948ce2f5dbce86e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ifileopendialog.dart", "hash": "e1b16ab85c86942cde8fabfa972fba9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/uk_messages.dart", "hash": "51742002c6a4604b5f85ecc74b2a197f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/locales/locales.dart", "hash": "3e9d663c9bd7d7fb115cc43874774500"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtablepattern.dart", "hash": "f0583593722d8dbc8d76df7f7df11dc7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ec.png", "hash": "cbaf1d60bbcde904a669030e1c883f3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/imodalwindow.dart", "hash": "7837848fa5cbb9801cfadd3856d0479e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispellcheckerfactory.dart", "hash": "419b1d6dad30c44e241a804453f78d56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/delegate.dart", "hash": "5fc1a25f60cfa0a0280878377348c63c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_member_name.dart", "hash": "2ef397117616f6ff779ed0ab2dd0d61d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "055a5c4a10cb9bc9f1e77c2c00e4ef9a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "52beedf1f39de08817236aaa2a8d28c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationcondition.dart", "hash": "0469c2fefb6084f264cd0df8bce7263a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/nu.png", "hash": "c8bb4da14b8ffb703036b1bae542616d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/id.png", "hash": "78d94c7d31fed988e9b92279895d8b05"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "2c777edec67bbb084e5608fb5f6b495b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/number_format_parser.dart", "hash": "61a0deef2a4f0ebaed506bb2a22c5185"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/th.png", "hash": "d4bd67d33ed4ac74b4e9b75d853dae02"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "96b4be28e9cb48156c65de35d7ccefba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/indicators/ema_indicator.dart", "hash": "b5e6d959819d3da91dd2aa6414d9f3ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/repeating.dart", "hash": "282aa0046bbbfcbc30050f7fab282778"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/macos/macbook_pro/device.dart", "hash": "aa541792d051fc6569838be6d46315ab"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cr.png", "hash": "475b2d72352df176b722da898490afa2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "db24bbb74875ecb216e8445bc10a0714"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/enums.dart", "hash": "a71d2292a5f598a6eea9a8ce5f3c5783"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/in_app_update-4.2.3/LICENSE", "hash": "d2646adca7fe74da01b012dafa46c900"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/widgets/draw_dot.dart", "hash": "1fc98e19080a0d4b097eb1154686d1cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zip/zip_file_header.dart", "hash": "df75f6f36187d9129fd7f1b2041836a5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mw.png", "hash": "efc0c58b76be4bf1c3efda589b838132"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "a732cf9cb336d70db5c1145f0e468953"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_mixin.dart", "hash": "0f5d8dd74761633229f5cf2fd6358e05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/stacked_column100_series.dart", "hash": "3d291ce8ececb84aac2407cbf565b6d7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/theme/theme2.dart", "hash": "924659e6acc8a7e5095f26987b341aa0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sv.png", "hash": "994c8315ced2a4d8c728010447371ea1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "0f2a1a61119c0bef3eaf52c47a2ebcf4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cl.png", "hash": "658cdc5c9fd73213495f1800ce1e2b78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/state/state.dart", "hash": "9a092e35db02b61a977f0c3feda2a74a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "e5163b554926dc261b556dc5d94245d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/end_element.dart", "hash": "813218451c1d8dd310e1233bd4ca7a4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/ca_messages.dart", "hash": "c681ed2471dd12d4d76912530c825c70"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sc.png", "hash": "52f9bd111531041468c89ce9da951026"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "2c5021ff8faa0330f66b1c501e8d4b22"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "008b3ea4691331636bbea9e057357ceb"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "7018ea64a9aab18f27a10711285d7573"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "5d7b0ee48c302285b90443514166c2d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/iphlpapi.g.dart", "hash": "2426e2644b69a745c9d477194b9b572e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/py.png", "hash": "6bb880f2dd24622093ac59d4959ae70d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b5eb2fd4d6d9a2ec6a861fcebc0793d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.2/lib/types/auth_messages_macos.dart", "hash": "03646ad7ecc4178fcf9509961aa7e424"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/pk.png", "hash": "0228ceefa355b34e8ec3be8bfd1ddb42"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "f209fe925dbbe18566facbfe882fdcb0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/fo.png", "hash": "0bfc387f2eb3d9b85225d61b515ee8fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/macos_device_info.dart", "hash": "2dad016b21ffd8671999ec7fee53d20c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/netapi32.g.dart", "hash": "1c6d490a13baec49a9edb03d5fb8a00e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/propertykey.dart", "hash": "6b00c4c5c720216a682e1018fb591aa3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxfile.dart", "hash": "873012eaf19c72c50b8622e17c72106c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "a0816d2682f6a93a6bf602f6be7cebe1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mr.png", "hash": "733d747ba4ec8cf120d5ebc0852de34a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/intl.dart", "hash": "f0dd0e0193ab6bc6a1dc2a6cf6e1cd6b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/number_parser.dart", "hash": "b8a405a7e5ea8001bb0ab36de015ac6d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "4aeb4635d84df42e6f220aba366af7d9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "34371da200382409d181bf9c3fcaefc7"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "732535ba697d95c80d1215c0879477f1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/qa.png", "hash": "55303011ca557cd3f1fc8ae7067d23a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "7bbb6aab4e83fc272886a39c92157201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/bloc/dashboardBloc/dashboardState.dart", "hash": "21801964bc2aeeaa3cf9b684b248cfa7"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "e758d8d6b65597325bd35b5dc769c7a2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "578ff911d6e70b239fd629f5a0206fd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/constants.dart", "hash": "195aceb9dfe0dacbf39711b8622ce2b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/letter.dart", "hash": "35ae3adcf5e51919e36509ef828107a7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ni.png", "hash": "6985ed1381cb33a5390258795f72e95a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "4eb84c94445470d8bb6bb8e2666aa51a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/comctl32.g.dart", "hash": "d847eca68c58e6b76393b62dc26a7c0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/utilities/utility.dart", "hash": "041cba912e242d3e2c23e25ce0c6e648"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/eh.png", "hash": "f781a34a88fa0adf175e3aad358575ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/cache.dart", "hash": "e0cbefa359309715e5101bce98eb65e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/dm.png", "hash": "b7ab53eeee4303e193ea1603f33b9c54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/where.dart", "hash": "4681dd3127db5960e33f162c004bf5d8"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/tm.png", "hash": "3fe5e44793aad4e8997c175bc72fda06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/token.dart", "hash": "6c8afaf3db5be20a458530a92ff971d5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/.dart_tool/flutter_build/53e50cb4642734bad20a387b49341e82/app.dill", "hash": "2061b7023bce87d0a408f9883986e26c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/data.dart", "hash": "d7fab9eeba6ce2b3fae0a93d5622ac93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "e85b30de1963bb6981d72b6027a66dd4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/de.png", "hash": "1b22a85ae7290aae77b9270667a57c07"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mg.png", "hash": "a562a819338427e57c57744bb92b1ef1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/options.dart", "hash": "e64d63aabc0975a7e9fdb384598c2f8f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "85814d14dae3bc1d159edd0a4bef48e4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ie.png", "hash": "5790c74e53070646cd8a06eec43e25d6"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "38c6297c7e2085554452d28299d29a09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/fk.png", "hash": "0e9d14f59e2e858cd0e89bdaec88c2c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/daterangepicker_theme.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/flatten.dart", "hash": "481d21ef07dee6f82302a015f989b597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/_inflate_buffer_io.dart", "hash": "de2ee2841614931c031c4596eaab3cb7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/LICENSE", "hash": "96ed4c0b2ac486bba3db2c5d2a96afc4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/element_widget.dart", "hash": "ff09f04999cdebeca29327de73a516e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/tool_panel/sections/subsections/locale.dart", "hash": "09a31fe17a62dff071d28adb07734402"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "f26e2cb53d8dd9caaaabeda19e5a2de3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/fm.png", "hash": "d4dffd237271ddd37f3bbde780a263bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/bs_messages.dart", "hash": "6ba111f5b4baa3239a925367cb3bbf9c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/fr.png", "hash": "79cbece941f09f9a9a46d42cabd523b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/sparkline/utils/enum.dart", "hash": "c9cd084b05b28db2b5ea31e2efaac064"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "8a39bdc324d0ff25097784bd98333c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/LICENSE", "hash": "54ca5f4345f518f70f047f1f2e585945"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bs.png", "hash": "814a9a20dd15d78f555e8029795821f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationelement6.dart", "hash": "e2688ec0f1c08b36b90a60cddc63b384"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iagileobject.dart", "hash": "4bc403cec1c5846051bca88edb712a8c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtextchildpattern.dart", "hash": "002fd240f385a66281c63dea9b31c069"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/interactive_tooltip.dart", "hash": "fa45c1f698d3de4016eb4a51b90a5cc1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "50dfb9886f462e2b3405f0f8d23f179b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/color_scheme.dart", "hash": "10c2939e7ee569cef0454be6b052ec24"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/excel.dart", "hash": "b2bf932c038e0998c27b119d52132a6b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/_file_content.dart", "hash": "10a182ab0a81c3e666f25f65d8f84766"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/src/consumer.dart", "hash": "38c2b67895c0418bce6750d3751a5b26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_properties.dart", "hash": "953396d57b69e0e889d9dfcc4f7fdabe"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "a9e3af96f170745db1c281777cb6bda9"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mq.png", "hash": "446edd9300307eda562e5c9ac307d7f2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "2fbba4502156d66db0a739144ccce9a0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/kg.png", "hash": "a9b6a1b8fe03b8b617f30a28a1d61c12"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "82d1200fedba087f85961d6b1b9332fe"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mv.png", "hash": "69843b1ad17352372e70588b9c37c7cc"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/aw.png", "hash": "8966dbf74a9f3fd342b8d08768e134cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudiosessioncontrol.dart", "hash": "db4827f3013417baab4977d3f19afb1b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "ed5548873fcf5a0a5614fc52139600b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_breadcrumb-1.0.1/lib/src/breadcrumb.dart", "hash": "9764eacf3833542bb10f2d8d2292bef9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/winrt_helpers.dart", "hash": "e2f61b143b6eaca3f6291b32388812df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/lib/src/string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/theme.dart", "hash": "0912cab0af816962e8423a19bf4cef9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_13/screen.g.dart", "hash": "3553dacb0b7625536d630c6a770121e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_enum.dart", "hash": "4817a73df1c313cf6a6eb86774e7fc99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/src/async_provider.dart", "hash": "3a2d20718f772fbb710aec7dc5e0bf80"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ga.png", "hash": "fa05207326e695b552e0a885164ee5ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/url_launcher_uri.dart", "hash": "3cb04add978cf19afa2d0c281e4c80b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "48b13baf494b39e894252da0a0f6e8c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/devices.dart", "hash": "f53fda85c80a5ec1e55a9f074f666526"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "ec48414c6983150c30241ba7128634fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/lib/url_launcher_ios.dart", "hash": "cf4a1121f45b68cc0ee35474061ba9ed"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/yt.png", "hash": "6cd39fe5669a38f6e33bffc7b697bab2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/pyramid_data_label.dart", "hash": "9d0e1fd59076ba39137bc683ecd330c9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "0e3d746a279b7f41114247b80c34e841"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/lib/pin_code_fields.dart", "hash": "cfcd7ef0cfb38b2362ab619bbd34bbd8"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/a4.svg", "hash": "79b0fcb5a4a0400002c17611c2f95ab3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.1/lib/shared_preferences.dart", "hash": "698b47b813b0194cf3adacff5906a585"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ci.png", "hash": "da9d5728161f286a4b5dfe34ff01a83a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/indicators/sma_indicator.dart", "hash": "1f2c1c743f05d6490e75df1ecd215087"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "a108c1a02c56f9162ede59a7c30ed41d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ki.png", "hash": "69a7d5a8f6f622e6d2243f3f04d1d4c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/charts.dart", "hash": "d967291d84ba78afea7530d8d51c49ca"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/noentries.svg", "hash": "d4291fa0756899ea5c38233941a05d25"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "cc4a516908b08edff4fade47d6945e5c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vibration-2.0.1/lib/vibration.dart", "hash": "ee94b77708bcc07d490b06e53916819d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_se/screen.g.dart", "hash": "4435762d7c93d78fa9a93e425bd7f4bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/tool_panel/sections/accessibility.dart", "hash": "d1283bce98e03abef7bcecd836ea3a9b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "73f043194b9c158454e55b3cafbdb395"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/prefix_name.dart", "hash": "fbb3e43ae57262b3fc190cb173a7b5bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/geometry/matrix.dart", "hash": "3561421bda06180a972759e28276d069"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/dashboards/TrendsChart.dart", "hash": "d522635fa07c22e51d8ab8f57838a48e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/a1.svg", "hash": "ab1a7a98dfd129b91c50c89d7684558b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/lib/local_auth.dart", "hash": "32e01ce1b6c445348bbeb73c4610288b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestospackagedependency.dart", "hash": "fd0e866e44796643d6fad18400ea6a77"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "98f06a29791e4f6ffc1ccefd18f323fb"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/td.png", "hash": "51b129223db46adc71f9df00c93c2868"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/vector_graphics_compiler.dart", "hash": "cc23c83ee1c9e80d4a9f0c262c69f17f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cw.png", "hash": "db36ed08bfafe9c5d0d02332597676ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/oneplus_8_pro/device.dart", "hash": "9a692dc46ec1cf4738e2138e2ace638f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/local_auth_windows.dart", "hash": "0aeba1cff194006a9d8e502e435d4730"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mx.png", "hash": "b69db8e7f14b18ddd0e3769f28137552"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/he_messages.dart", "hash": "faaa82b4f77f68ed0a2ded42fa7da7ef"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "cd6b036d4e6b746161846a50d182c0b5"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "4eede9144b4c0e4b14bd426654183174"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "525e57b6ade38da2132c8ddb0ea78547"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_y.dart", "hash": "853e7e8b3898f3c0055ae0ae1630e229"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/registry_key_info.dart", "hash": "01167f5726fe27d877af4652c1e404a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/resolver.dart", "hash": "8b864131bf90f8b83b363321907aadaf"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "84f94e87e444ce4ebc562b2707348a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/pattern/parser_match.dart", "hash": "d742d41268dec3da5e669142ae344928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "0c402ad9ba3f3e4d7f45f24b27447ec2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/optimize.dart", "hash": "a4acaadf6a817daad4c485f9c6741bca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zip/zip_file.dart", "hash": "1382bc64a97b2cc89b01416b5dbd917e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/bzip2_encoder.dart", "hash": "9b73a39d1b73cae05229c5b145924fa8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/subtree_selector.dart", "hash": "e5f1007517b62683935488c5189ebc5d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "8ae04de7c196b60c50174800d036642f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/device_frame.dart", "hash": "385268266af91742ac449beb0b043ed7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/images/appicon.png", "hash": "f13e54be2a299ec3c7f7912d76c67e3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.17/lib/svg.dart", "hash": "6f529a25fe7e9fe0866e4257b171aeb9"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/se.png", "hash": "24d2bed25b5aad316134039c2903ac59"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "b1bb8356cca8b86afca314ab4898a527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/gzip_decoder.dart", "hash": "903c5366b070dfc4a51c86a87aaf599b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/super_tooltip-2.0.9/LICENSE", "hash": "ea5a848db98bd4fbe23030ff5bb297cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/utilities/enum.dart", "hash": "91e3bbb55d514bc846ce2d82d2c1903a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "f49291d1bc73b109df4c162db10003d2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/fj.png", "hash": "6030dc579525663142e3e8e04db80154"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gr.png", "hash": "86aeb970a79aa561187fa8162aee2938"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/hn.png", "hash": "09ca9da67a9c84f4fc25f96746162f3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationobjectmodelpattern.dart", "hash": "db8a81e510b416095ef477688165eee5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/lib/src/models/haptic_feedback_type.dart", "hash": "84167c68540bec8d31920df214546857"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxpackagereader.dart", "hash": "2c9b99820a7ba58eea5e30ca3585c24f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "ca759e06438affc7dcbdd9c4d8f0dbb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/my_messages.dart", "hash": "ab558fa0781e42f7978f12b30bc0653e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/er.png", "hash": "8c4feba235695208189a3ba0632ccd08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/sheet/font_style.dart", "hash": "7964b28b94da20c7e5ded080c17f1eaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/case_insensitive_map.dart", "hash": "b7daa46d4dace857514806769032077d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/stacked_bar100_series.dart", "hash": "1f24d65ed9e7952e50ad6b4c430becea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_a.dart", "hash": "6f31150716f793ef18c1216f785c7e6e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/painting.dart", "hash": "4bd60bd8ede4b9dad954493d26d3e586"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/me.png", "hash": "74434a1447106cc4fb7556c76349c3da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/src/deferred_inherited_provider.dart", "hash": "59ae3a059b0ba1677002bed66f3b8c2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "d8060c05b658b8065bc0bfdff6e4f229"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtextrange.dart", "hash": "8f76417391b910fe0956d6404b59e144"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/noise.dart", "hash": "e9fe7ebb2a16174d28ca146824370cec"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/mail.svg", "hash": "fefd20c8a85e5052ba3c4c3b02e04f1e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter_localizations/lib/flutter_localizations.dart", "hash": "dc4a72832b8b4320c2130207ff161b58"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/km.png", "hash": "204a44c4c89449415168f8f77c4c0d31"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/fi.png", "hash": "a79f2dbc126dac46e4396fcc80942a82"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bz.png", "hash": "e95df47896e2a25df726c1dccf8af9ab"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/it.png", "hash": "99f67d3c919c7338627d922f552c8794"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/pages/DevicesPage.dart", "hash": "3d2477ea4aee295546445ef1bb4f6c60"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gd.png", "hash": "42ad178232488665870457dd53e2b037"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "9796b800122953ccb2c3f40ba2120a94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/winmd_constants.dart", "hash": "0cfcbe0ce66e9725eacd8c5fbc6f604a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iinitializewithwindow.dart", "hash": "0748bf03bcf37edd1d571959e45a5cc0"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "c8a14f8ecb364849dcdd8c67e1299fb3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "726a60283ea6c3a38fbb1ea6139cb4f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/excel.dart", "hash": "a90f318851e631579f40f5a1387d8953"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "ef5fc00d685cd2a36c4de80e1c7e3a8f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "262d1d2b1931deb30855b704092d3cb4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/lv.png", "hash": "6a86b0357df4c815f1dc21e0628aeb5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/comparison.dart", "hash": "643ca26571c2ba94477233dbb914b1ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/number_symbols.dart", "hash": "6c1b7903629a7ad4cb985f0898953db1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "8e870f9527626d34dc675b9e28edce85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad_pro_11inches/frame.g.dart", "hash": "b8fd1c8231d80cd1eceb58d8c1fd4afc"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/profile.svg", "hash": "5bf9ecc6dadcb804e33db40e7f6b4b12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/imetadatadispenserex.dart", "hash": "6ee584441f30f72cea8a75f9b861591c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/mutator.dart", "hash": "e105e8d3303975f4db202ed32d9aa4c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/lowercase.dart", "hash": "044ac7a861e88a6b5e7e2d2c59ccb7bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/chunked_coding/decoder.dart", "hash": "dbff400b121e6f844298946531d490a3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "dd109d67b92b9fbe6e0051f0c890c903"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/bzip2_decoder.dart", "hash": "9ecbe771e2dd8678e48ad5b690b4aaed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iwinhttprequest.dart", "hash": "b44c83e3276e2ebad7c43ed8d7beae72"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iprovideclassinfo.dart", "hash": "c90759e0e90f88fd2b4f177ec55cb4f4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/as.png", "hash": "830d17d172d2626e13bc6397befa74f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/image/image_info.dart", "hash": "55c2cf99367a0cfdd7ef8370df299c66"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/me.png", "hash": "74434a1447106cc4fb7556c76349c3da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationelement9.dart", "hash": "7339ec709c898b8e442a3a02e63f3e6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.4/lib/src/shared_preferences_android.dart", "hash": "6f05b68df1b893e73008d1831589377c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "9ea1746a0f17f049b99a29f2f74e62ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/src/bloc_provider.dart", "hash": "16fcb890ececf0ad4765106051e5fd0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/imetadataimport2.dart", "hash": "9cea354b06cd8542da4dd38ff9fc01e9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib/src/bloc.dart", "hash": "55baef8de40a0a851a554dc20cd5ee87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "4b50828d394e7fe1a1198468175270d9"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/am.png", "hash": "2de892fa2f750d73118b1aafaf857884"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "045c779ec8564825d7f11fbbd6fb2fa1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudiosessionenumerator.dart", "hash": "befc59cd40e14d926671211e72495596"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "5da121a0d3087e7cf021bfcdeb247b77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_3.dart", "hash": "050f96bbbf01a1f86e208d7d8cc08901"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/src/bloc_listener.dart", "hash": "d474570a09b261626c01cd3ba506ecbf"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/dashboards/TrendsChartCombined.dart", "hash": "b66a6c018feb48d8c8a250ff1828a38b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parser_exception.dart", "hash": "a62996936bad6c27697a35bed070547d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "e556497953d1ee6cd5d7058d92d4e052"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "920b63c794849c8a7a0f03f23314bbb1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "d2694042e337ac1f2d99602c25be195a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bt.png", "hash": "3c0fed3f67d5aa1132355ed76d2a14d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/_flutter_widgets.dart", "hash": "26b949575b3894692410f61281c24387"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/slider_controller.dart", "hash": "ab8fb3c17b70e86319c9b2a8503fba1c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bj.png", "hash": "9b503fbf4131f93fbe7b574b8c74357e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/radial_bar_series.dart", "hash": "c2888eac1b4b484c952098eb9033978a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/be_messages.dart", "hash": "1b2008d0a65366a516f2d896c56e5d7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math.dart", "hash": "703f2b29a9faedbb501bbc2cd99ba7b5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ro.png", "hash": "1ee3ca39dbe79f78d7fa903e65161fdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_config.dart", "hash": "e0f2b097829216421823bda9ec381cab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/code.dart", "hash": "2d312745691a82b398796ad2f38ac63c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name_matcher.dart", "hash": "5c4dc37f36fc78823f785b92b944560d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.4/lib/shared_preferences_android.dart", "hash": "30bffdef523e68fbb858483fd4340392"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gb.png", "hash": "fc5305efe4f16b63fb507606789cc916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/utils/zooming_helper.dart", "hash": "ed2faa07d760572eeda90f6cdb94f81b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/number_format.dart", "hash": "76052188e777d0ca03128d3b299d836c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispnotifysource.dart", "hash": "c126b73764228fafd6b80ed5e2d7ff0f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/am.png", "hash": "2de892fa2f750d73118b1aafaf857884"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sb.png", "hash": "e3a6704b7ba2621480d7074a6e359b03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "8ece5be4aa5c8fa615288c4c8c5277a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/expression/group.dart", "hash": "f31a685ec42e95decf8c1937de3a5856"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "e37bb4fabbf2e61e9b7fbe06f5770679"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationwindowpattern.dart", "hash": "0d790476d9ddbae00b9e3f0076902498"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/utilities.dart", "hash": "db8fd891fdcab94313f26c82f3ff2476"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "853b1406f2756bef671f6d57135606f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/nodes.dart", "hash": "8608080cdfc143d462b0f9947dc0d7c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/lib/src/models/animation_type.dart", "hash": "e458b4e2ab58d58978f8dacd98903d47"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/bloc/dashboardBloc/dashboardBloc.dart", "hash": "2675b614ae05064bc432c167f61e4478"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/io.png", "hash": "8021829259b5030e95f45902d30f137c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/themetoggle.png", "hash": "4d3d2c6df1e9648dc47be8bd1af8e3f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationspreadsheetitempattern.dart", "hash": "f1cfa3a69ee743157de8de4ccdf51b58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_converter.dart", "hash": "2d069a48b5e0ffa386474977d2c91c90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/response.dart", "hash": "6b75392292c501321a02185f5f45f977"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "790dc5e1e0b058d13efbd42a3f46498e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "0ff55be19444856c892e701c475b20f6"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/date.dart", "hash": "86b720af61fd71f6566c9e8d42412e85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_se/frame.g.dart", "hash": "1d9fed00fc23319bbcb145eaf2433cad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "384c15d93757a08ae124e6c2edeb4e9e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "e0b4c38191be9320c3113762d2dfebbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.3.1/lib/permission_handler.dart", "hash": "12799db03309d9691359354e1413a0d2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/pm.png", "hash": "6cd39fe5669a38f6e33bffc7b697bab2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/cdata.dart", "hash": "008d33cc2aea11e7921ee238469947b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "2a374faf6587ee0a408c4097b5ed7a6e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/model/userInfo.dart", "hash": "d04add47af19f2f4c84957b0c9211826"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "ef24f0630061f35a282b177d372c00d1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "c761b80666ae3a0a349cef1131f4413d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/isimpleaudiovolume.dart", "hash": "654b609384b7b69890219a8d8eb510ce"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "307c2ee6ebc77b9995c2799e8e0bed81"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/tt.png", "hash": "716fa6f4728a25ffccaf3770f5f05f7b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/tt.png", "hash": "716fa6f4728a25ffccaf3770f5f05f7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_object_tree.dart", "hash": "eedac0b4fc9b2865aae62ba790f0e26a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/attribute.dart", "hash": "3a8ae5977fc932c86b4b61e92db7a275"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "f9646c35238459f46dd9d87783813f08"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/va.png", "hash": "cfbf48f8fcaded75f186d10e9d1408fd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "e3b1d07a31d08470207f2b668564a833"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/utils/loader.dart", "hash": "d41a1e01d42427f1d07c09a62966a302"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/expression/utils.dart", "hash": "8608f71f077e370ee14d37c711e6580e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/structs.dart", "hash": "b51cea8017e3cbb294fe3b8066265c7e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/om.png", "hash": "79a867771bd9447d372d5df5ec966b36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/scan.dart", "hash": "9ce6595770687511a1c77ace6f55bddc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.3/lib/url_launcher_windows.dart", "hash": "792062b629f33f12bf4aa68dd6601c50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib/src/cubit.dart", "hash": "399ba7cb9a9b1ade0886ad64901640e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/ms_my_messages.dart", "hash": "94def57680320cadc692ca68f43b1807"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_z.dart", "hash": "991a163a470f64b0222de6290e39d538"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/vn.png", "hash": "7c8f8457485f14482dcab4042e432e87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/axis/datetime_category_axis.dart", "hash": "441b461a7d2fdf7618881c194f54b2c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/declaration.dart", "hash": "0ddfa36e71f58e8be68202ab6901bfdf"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "b8c09bf358fcebf2f4c9214d1007536d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a06bb87266e0bac30a263d7182aaf68c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_13_mini/frame.g.dart", "hash": "4fea7dc0dda03f008d92804cae5373ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/adler32.dart", "hash": "fc03346c11301342d64a349d5c317c7a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/time.dart", "hash": "872d879ea43b6b56c6feb519cc12d5a9"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/vu.png", "hash": "ffbcd1822588e63b3ef0f2fcb230b179"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "97f7922aea45c38413930285b604bf18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/LICENSE", "hash": "e05d7b0b08e08d0c029b0b08954ba1d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/plural_rules.dart", "hash": "2241f880365723564463d0bec35a4ba2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/tl.png", "hash": "b3475faa9840f875e5ec38b0e6a6c170"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudiosessioncontrol2.dart", "hash": "18ce35bef6b656745428776b3aaaf4ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/_path_ops_ffi.dart", "hash": "9aad11ee7e005623c54a1c59038f9d4b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sn.png", "hash": "25201e1833a1b642c66c52a09b43f71e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mt.png", "hash": "808538b29f6b248469a184bbf787a97f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "29befe23f841cf5dd2dc7df24c13d88d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/dashboards/SummaryTable.dart", "hash": "0f71889fa713e722a9753ea4e6415a7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudioclientduckingcontrol.dart", "hash": "54a357c7c827b2616fd5e9ff6fccbfd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ifiledialog.dart", "hash": "dd9bdb173b854917c11832f369e59479"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "02f1d44813d6293a43e14af1986519ff"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "837da7ede58523b5aff0ccbb40da75ba"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/kh.png", "hash": "cd50a67c3b8058585b19a915e3635107"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iknownfolder.dart", "hash": "9805639600096c1f056657f418f6703d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/declaration.dart", "hash": "7b254933211feaa1ea185b61dc9b12af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/char.dart", "hash": "5755449cdace9c88111718f61f6c25e8"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cc.png", "hash": "126eedd79580be7279fec9bb78add64d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/dtd/external_id.dart", "hash": "348e54c1032cec91d7a1a5cfce8c2098"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "0f6f972f6232b9d18cf00a9fa432127b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationmultipleviewpattern.dart", "hash": "6a7998938486af5d266f1b9072166647"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "40587a28640d3c90ad2e52fdfbcd7520"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomation.dart", "hash": "e1980812801e0d89e39cfa0bb4cf7fb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/istream.dart", "hash": "752db229137baa4ff1a3eccbe3cf69b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterable.dart", "hash": "0ea87086ab38d0a0e292321e807293f8"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/descendants.dart", "hash": "ffaf08c52f141dda6e8be50b3e46ea50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/ancestors.dart", "hash": "3f842dc9d82d8b21557bf598ff4ec83b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/nr.png", "hash": "9f15185ce7927f35c7610230cee0b0fb"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gm.png", "hash": "088268bc52242c3da421ccfe58b491b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/tr.png", "hash": "0100620dedad6034185d0d53f80287bd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "a91b4b0d0d10b955e8973126cf288ea4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/theme.dart", "hash": "fc4785be7521d2b2cd151d46d79cd5ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/nfcicon.svg", "hash": "fb3685554026dbe621d551b0700157bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/lib/freezed_annotation.g.dart", "hash": "e9358b84882d088105b22ba78da5db6c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/fr.png", "hash": "79cbece941f09f9a9a46d42cabd523b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/behaviors/trackball.dart", "hash": "cf6af6501f85a88e53a649257c265ed9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/core.dart", "hash": "0ca14634f99a2782c9127c87d0f792b2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "eafb3b31ec7cebf556a529810d6f649a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/data_label.dart", "hash": "bc020e7f83db282b8e7f480016f7c586"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gp.png", "hash": "6cd39fe5669a38f6e33bffc7b697bab2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/definition/grammar.dart", "hash": "e0633b7a48c9c4a43b84e885dc2049f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter_localizations/lib/src/l10n/generated_widgets_localizations.dart", "hash": "73239c51ff94dac8611a150a9a087d19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationelement3.dart", "hash": "e0417e8f067bf4a25edc299853bfe050"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/biometric_type.dart", "hash": "5c67019c52b8cc1c9e1d211aaca0f2a9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "16859f5e798cf33fc3c76a7a3dca05d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ierrorinfo.dart", "hash": "aeb565e28b1e55ec3794a6b88d975aa5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "hash": "092362603d55c20cda672457571f6483"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestreader3.dart", "hash": "d71f66fa79f435e0e9b2a8152443a331"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationstylespattern.dart", "hash": "7326647ec0ab13c912ff9965ccfb4081"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/lk.png", "hash": "56412c68b1d952486f2da6c1318adaf2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestpackagedependenciesenumerator.dart", "hash": "c81b77e6c86772f05b86739d8ba68b14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/src/multi_bloc_listener.dart", "hash": "e9a4bf441b837e108cf538fab788efb6"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "13b920f66eba39405ab6c5487e5fc3f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/base_response.dart", "hash": "4bc0f22efc6874ea847b62d2dddb16fd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "036fc28dc98388abec4456e8142c530f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_7.dart", "hash": "c3e5aaaf36524bf9927e80f60f3b0bdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/registry.dart", "hash": "a993ca2b8748f43da4eb160b12a15718"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/profile2.svg", "hash": "8c79675c696b4c46bbedbd13735d714a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/bzip2/bz2_bit_writer.dart", "hash": "edee0987f0b4bb1e5bb4e7a9f3e5135e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "a2587417bcfd04b614cac5d749f65180"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mt.png", "hash": "808538b29f6b248469a184bbf787a97f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sms_autofill-2.4.1/LICENSE", "hash": "3fd57ddbfe10667dcdb7ae8598a7f7d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtextrangearray.dart", "hash": "5a8ea03396d41d3b76a510289dee5d25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/utils/scrollConfig.dart", "hash": "e4ff10d4492f10d7ef0626fc02354ed4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "55f7619e20765836d6d1c7001cb297fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/nm.dart", "hash": "7494ac5a5e8b9d56894cd383fa6e9d91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/definition/internal/reference.dart", "hash": "f25bbc73708cc35ac55836cbea772849"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ienumwbemclassobject.dart", "hash": "9419b7e38f497126339e8cd2ccba9e66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/limited.dart", "hash": "bfc3692929b6ffa40605428f3cc70e86"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "9b1cee1f8aa8b638cad928232383b02b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.15/lib/src/_debug_io.dart", "hash": "118b6a62408f796e238c61d271e5146f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/generic/laptop/device.dart", "hash": "0e3a7abd6c07a8ed59506a9b17f9a755"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/storage/storage.dart", "hash": "dc2c3c1e164ed89549d90bca2414a655"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "0db5f597f1cc6570937e6c88511af3a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationorcondition.dart", "hash": "037c1b4cc41d0a66ea6134bf054ac095"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/url_launcher_string.dart", "hash": "27e6c510107a34001ef90f889281633e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/path_ops.dart", "hash": "9ddd18d11aa8b6e12c10e714c24de8e2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "a6d730f196620dffe89ac987b96ef6c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/tool_panel/sections/subsections/custom_device.dart", "hash": "c5c41c51106a46118ceb4f30f7f1e744"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomation2.dart", "hash": "34d140191c4affc37f3716de1b46854a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_mac-1.0.3/LICENSE", "hash": "54ca5f4345f518f70f047f1f2e585945"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "3ecea4d9c25299b0ea66c58256909437"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "785eedcc96fa6a4fcc7c81a8736a7427"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/containers/PasswordController.dart", "hash": "3b244a7b637cdb4e174a78dea4b60d08"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/flutter_logo.dart", "hash": "985cf5499dc6e521191985f55245a22c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad_pro_11inches/device.dart", "hash": "f3c7d6805ef2cf63e2abdb12a41d2ad9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/samsung_galaxy_s20/device.dart", "hash": "5dd027eb4f5e548685b5b9560dc3e369"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/lib/src/gradiented.dart", "hash": "85a09b8b17755bf82c66cdf1bea4bd9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudioclockadjustment.dart", "hash": "d25601f97655927dc9fd147438eacfad"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/sequence.dart", "hash": "0b1a431f52b54788ec3e9b6da7d87909"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/registry_key.dart", "hash": "b4b62a6be8e303f9d9a5b5408bf9106c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "355538055d623505dfb5b9bae9481084"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad/device.dart", "hash": "1c7de286e0b03239a2ae9ab82906d464"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "9a31689295b300aa8ab12d29fb8853ff"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ienumnetworkconnections.dart", "hash": "ee244b933f07447928851d56170a8050"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_5.dart", "hash": "a2f8f444e0f2d008fef980dd8df0bcac"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mf.png", "hash": "6cd39fe5669a38f6e33bffc7b697bab2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/utilities/constants.dart", "hash": "c4bc2654fc831ffde8e40874f2b8bfd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtexteditpattern.dart", "hash": "7b53b9344345e99b1ec1c1e6247b0f78"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/dz.png", "hash": "93afdc9291f99de3dd88b29be3873a20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/oneplus_8_pro/screen.g.dart", "hash": "6e1645b5c3ff4acf2c1368c86980daa8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_buffer.dart", "hash": "22acb270c1bb267ee16b3d64a3faa825"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/decoration/decoration_underline.dart", "hash": "621fc9520c739a8c8c2002fb79872106"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/LICENSE", "hash": "75ba7e8a7322214ca6e449d0be23e2ff"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "094b2c03ad4e0ef5bc1144e281142b2e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bf.png", "hash": "9b91173a8f8bb52b1eca2e97908f55dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.46/lib/src/messages.g.dart", "hash": "2f0a0bc8c94bf7cd746c5fa5d47040ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bg.png", "hash": "bb279455eec0de36199f1453c1c67042"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/idesktopwallpaper.dart", "hash": "74319ce8573194302792ea41f665838b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/clock.dart", "hash": "84ad21db5ba97deb809b65697546e39c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.2/lib/types/auth_messages_ios.dart", "hash": "84f8da617ffbe5c187ab3340e6d17cce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/windows/devices.dart", "hash": "c7227aa4516f24f7ba2d32fc5b4f8e5a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "8e7a18cd739e24a264facecc38379085"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/linux/devices.dart", "hash": "3a0bb0f0024b8a22cd812305f2ae6255"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/info/info.freezed.dart", "hash": "c483f997659b3ec74e3404d782ab0698"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/api_ms_win_core_handle_l1_1_0.g.dart", "hash": "cc8236ed613332ed202cadb26db1c743"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.15/lib/src/listener.dart", "hash": "65ec707b3eed166828768c1ac1cc45f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/range_column_series.dart", "hash": "00143f9afa62c1fd419470625260a7e0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/main.dart", "hash": "e65c44b1468a827972714d2117df08f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zip_decoder.dart", "hash": "bacc4b237d160c70f1af46b56cd45495"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.2.3/lib/permission_handler_platform_interface.dart", "hash": "b2cedc16db668bbe5b025bbbba69c6d5"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "b5bd9d15c10929b4a63ea0df649e2d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtransformpattern.dart", "hash": "d374a7295ed13ae994b36d002890225f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/qa.png", "hash": "55303011ca557cd3f1fc8ae7067d23a3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sv.png", "hash": "994c8315ced2a4d8c728010447371ea1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gl.png", "hash": "d09f355715f608263cf0ceecd3c910ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "26312d25d45c45d94edcfbaaec9217b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/generic/laptop/frame.dart", "hash": "6d27c2b574d23508e280a63e1376c887"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gh.png", "hash": "c73432df8a63fb674f93e8424eae545f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cy.png", "hash": "9a3518f15815fa1705f1d7ca18907748"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "2627dee7fb363a5bb1cbc919699bcc84"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "6a7d9ee6c8fae5e9548911da897c6925"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/containers/customButton.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/method_channel_connectivity.dart", "hash": "3d18e1306d78e114f98c9dc311fbf158"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ps.png", "hash": "b6e1bd808cf8e5e3cd2b23e9cf98d12e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/samsung_galaxy_note20/screen.g.dart", "hash": "4f4cba97005800e3d6a1875e4fe70841"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/utils/enum.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/misc/label.dart", "hash": "7de7aec8bf9b53488692403a3feb7672"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/stopwatch.dart", "hash": "f38a99a51f4062e7861bb366f85265d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/hr_messages.dart", "hash": "dbd2ba4fc59d8c8ab6e4cfa52bc4f4ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ee.png", "hash": "54aa1816507276a17070f395a4a89e2e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "f26f519ea124441ec71b37df7cfa1ee9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/behaviors/zooming.dart", "hash": "47b87208383753b0d42941c1f2989bab"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "3f7c50b425818ea563c8459cfd6f9d5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "b33b1182e92dc3469db2563a33be2841"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_0.g.dart", "hash": "872e5481eca8f8f757d139b5e4988053"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/LICENSE", "hash": "c458aafc65e8993663c76f96f54c51bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/predicate/any.dart", "hash": "35536afe2f48001aa7c588b131051b83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/hexagon_dots/build_dot.dart", "hash": "2ac609639c4ea5f736ac3b4fbf67798a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "hash": "7f37ea646d3e1b9f923f3af623128a0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/possessive.dart", "hash": "e67b8312f8af090a5eae36871ab6b65b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/area_series.dart", "hash": "8eaff5cc45c83adbbe2cfd6d683ea1ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/circular_chart.dart", "hash": "caf096745082f4b089f2beae78dd7912"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/skip.dart", "hash": "be231020db4ff03ccedf0cab8d50d12d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gw.png", "hash": "25bc1b5542dadf2992b025695baf056c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/predicate/pattern.dart", "hash": "d881c458d06573eb887bdf0f3ce9f586"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/types.dart", "hash": "4a1d1bdbd4e9be4c8af1a6c656730a66"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/pw.png", "hash": "92ec1edf965de757bc3cca816f4cebbd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/da_messages.dart", "hash": "d8b7bf986a7a310048810965eb89e693"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "39e18667c84e363d875147cc5dc6b2fa"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/card.dart", "hash": "90d9d45eef80ac53b194a71da4e10975"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/activity.svg", "hash": "4412169e734a95d2f50e22632dd397b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_interface_name.dart", "hash": "4f835012742ef22df8c85292594f9823"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "a103dff72cbe4ef64a02c37dbfdc752d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/writer.dart", "hash": "21a6f7aab6021cd2c8c69f9cd78ae36d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/utils/alert_message.dart", "hash": "bea25d958974371262802a237e9aea72"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iwbemclassobject.dart", "hash": "fa0457adc89723d08bb20eddf3e89555"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad_pro_12Inches_gen2/screen.g.dart", "hash": "9239a30499601be2c6608efb7e85f4a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iwbemobjectaccess.dart", "hash": "cb5493b3fb9ca309e2cae9a641029cd0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sc.png", "hash": "52f9bd111531041468c89ce9da951026"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mo.png", "hash": "da3700f98c1fe1739505297d1efb9e12"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/images/1fqC.gif", "hash": "63317f508ac8d60e972b37c4e3ba150f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/stacked_line_series.dart", "hash": "0a208d9e116e3be62194613107df085b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/src/multi_repository_provider.dart", "hash": "67ac9b26d6e260d21e668141046d44ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad/screen.g.dart", "hash": "9d3f5ff3ff5fdd4d6c48f0dd14fd851d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter_localizations/lib/src/l10n/generated_material_localizations.dart", "hash": "b957d16550e0752baec1db36906212ee"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "07664903d8026f2514b29b786a27f318"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_j.dart", "hash": "998746037e3416b31d33881bf69a4148"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestreader4.dart", "hash": "c475dfaacb936bfc5773b55b5b7db7a3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "cb745b78bdb964c02c1c4a843b9c1e7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_name.dart", "hash": "749e18efee29d6925d7c55e573d3eb2f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "7c4df8be3ef1b8c4564f6aa3c64ba65d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/app.dart", "hash": "66bb0d42812dbdcb77a351f5d79c74a4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/LICENSE", "hash": "39d3054e9c33d4275e9fa1112488b50b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "a2aa815908f2e15493e374b9380e558a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "hash": "2061b7023bce87d0a408f9883986e26c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/vc.png", "hash": "a604d5acd8c7be6a2bbaa1759ac2949d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ienumresources.dart", "hash": "08a61adc8ecc7216c84a455539fd75ad"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "0763a220fcb5274b6c228b8b440ddb2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_children.dart", "hash": "7c666bff17f2cfae821f93f0c5e66a64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/src/inherited_provider.dart", "hash": "37d8bb0465f404f16623cc756b2f224b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screenshot-3.0.0/LICENSE", "hash": "fc468b90842a0642c84e0ac0de726d34"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/np.png", "hash": "35e3d64e59650e1f1cf909f5c6d85176"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ge.png", "hash": "93d6c82e9dc8440b706589d086be2c1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/multipart_file_io.dart", "hash": "8830333c78de58ad9df05d396b651ef7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/lib/string_scanner.dart", "hash": "184d3b79d275d28cd02745b455041ee6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/not.dart", "hash": "d4acdced936c4825eed27ed61fc28660"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/se.png", "hash": "24d2bed25b5aad316134039c2903ac59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/petitparser.dart", "hash": "6ed88e961d0aa7c9506e4fa969be1a92"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "e3d917994e875601c2dadaf62de546f2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "f6345e2a49c93090bc2e068a0a808977"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ws.png", "hash": "8cef2c9761d3c8107145d038bf1417ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/nl.png", "hash": "67f4705e96d15041566913d30b00b127"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudiocaptureclient.dart", "hash": "98c8a48ba3ece7573f6f3a9bfde19840"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/src/theme/maps_theme.dart", "hash": "78ab54563a72213d56b7ec99aae251a2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "74708cb40b7b102b8e65ae54a0b644be"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "0cf873bc441372ec89d746477273af13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationelementarray.dart", "hash": "41baecfe75bc82e8dae966eba92c23b7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/je.png", "hash": "8d6482f71bd0728025134398fc7c6e58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad_pro_12Inches_gen4/screen.g.dart", "hash": "1a0766d8c504041e9aa17072451772b0"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "91bf94aea1db708a8378fa41de066d33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/winspool.g.dart", "hash": "0f22a1dc771ec0ad975c574b1ce5dd70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/getuid_linux.dart", "hash": "cc4abe2eecf823ea14c55f9c5c09e203"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_write_buffer.dart", "hash": "63d2768cdd6ab5a282fbb6a86c237b78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.0/LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/device_info_plus_linux.dart", "hash": "c273c5105a2ca17896243db15f9b07ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/parent.dart", "hash": "210257ed62edd783098ed34d7cfb0204"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/tessellator.dart", "hash": "77c9544896f5a68ee56c9ca44d686cb6"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/containers/customTextField.dart", "hash": "9b91a5557061960db6ae4864486613ad"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "8b15d222f5742b46bf55a4ef4cbfd6e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/json_annotation.dart", "hash": "532a272d043c3dccd91b63d1b428dac9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "f30e48d0892af0c99b54816673cff9ab"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "cf5dc26d65244c12416f3411c6d79996"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/search.dart", "hash": "66a927b3f610db5ff8c77a6ba3ccee0b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gu.png", "hash": "babddec7750bad459ca1289d7ac7fc6a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "0a731b52181a917a08ac96b525f7d96b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "5fe5b5ed3ec92338a01f24258b6070a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/devices.dart", "hash": "b8727472aa8379b27d80812dde81222a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mn.png", "hash": "02af8519f83d06a69068c4c0f6463c8a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/.dart_tool/flutter_build/53e50cb4642734bad20a387b49341e82/dart_build_result.json", "hash": "1f8e8e6dbc6166b50ef81df96e58f812"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "62f6d0411965eefd191db935e6594f90"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/.dart_tool/flutter_build/53e50cb4642734bad20a387b49341e82/native_assets.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/registry_hive.dart", "hash": "6ae62d29deefd524cb417afb7b9794bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_platform_interface-1.0.3/LICENSE", "hash": "54ca5f4345f518f70f047f1f2e585945"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/keyboard/button.dart", "hash": "3c800de1af1c2b2ca9232d837dfe2351"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_uuid.dart", "hash": "c9efc107e2b16a48d4e132bfcc679af4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "7b71540e417e6ea3f1134b4b677e0624"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "505f6c9750f9390c9e9e4d881092cef4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/sharedStrings/shared_strings.dart", "hash": "8f551be42981953413f562cfd1693d9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/funnel_data_label.dart", "hash": "31efb02787dbb341e3f74f35b8a631b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationelement4.dart", "hash": "a212841ba1b80a845ce3756241645d58"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "5be90cbe4bbf72b0264413e4ccb5c275"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ke.png", "hash": "034164976de81ef96f47cfc6f708dde6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/timeago.dart", "hash": "2a55233cc5459751a43d7acce89b6b0b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/_web_image_io.dart", "hash": "e88b0574946e5926fde7dd4de1ef3b0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/indicators/momentum_indicator.dart", "hash": "010b2b9fbfc944f459f7ea08e2efc6ec"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "18223495a47aa96889552c9834042729"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mz.png", "hash": "40a78c6fa368aed11b3d483cdd6973a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/indicators/accumulation_distribution_indicator.dart", "hash": "503f34e1210dc1c127e629ca1af47964"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clipboard-0.1.3/LICENSE", "hash": "e6dc97c8690b7ff4b1c02cf1aa946c69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_encoder.dart", "hash": "85ca5d0ad350ba37b698247c24cb70a0"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "a29f0df228136549b7364fcae4093031"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispeechvoice.dart", "hash": "e4db97d8d7acb9a9585f38b0df246277"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/at.png", "hash": "7edbeb0f5facb47054a894a5deb4533c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/nl.png", "hash": "67f4705e96d15041566913d30b00b127"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/size_extension.dart", "hash": "2be0cb639a3e7f80cf320e6a37f0d1aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/tar_encoder.dart", "hash": "10693856cc2bace0b2cd3b65fa24d974"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "f979a94d7bd35cf2a5168fbfb9bdcf1f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/a0.svg", "hash": "eb0c0eb2895d3e0c1216a8a5f4faf648"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.3/lib/flutter_secure_storage.dart", "hash": "5a944801c9b2bd3447f982168b31e46c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "fc0b4ef021be19542435a86743d8de7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/extensions/int_to_hexstring.dart", "hash": "73cb6deeb88fdcc320cf8e089d51531d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_base.dart", "hash": "ba78ae31f8b033543921d261bbe60dca"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "2adcbf9fb509dd8fe8864a702db29043"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sd.png", "hash": "93e252f26bead630c0a0870de5a88f14"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/carousel.dart", "hash": "006c00513de6bd421565ec6ffd776337"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/constants.dart", "hash": "9a463f361999508124d9da4853b1ba5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/sheet/header_footer.dart", "hash": "d3244c53bd9dba74ff9a0c98a42ba98e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "08c3fd9ed1607d3a707ffe9b3532218a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "e38cc213f0e4b4ed76471f4d70e20abe"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "2cf5ffb71954128b5e80f17a36bcde43"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/pages/RegisterPage.dart", "hash": "813ba0209227947eefe91ae0099d6cd5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sj.png", "hash": "f7f33a43528edcdbbe5f669b538bee2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/lib/src/utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/src/builder/color_builder.dart", "hash": "ca8b6ab8842245e5f7afe0302f08e293"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/inetworklistmanagerevents.dart", "hash": "a403f9be5cc42dedca5208fa2c104dd3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "8e043971337ae96a1e56aaf2256540ae"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "9af22b49fd7407bc0ef05667f139defd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "37f181e3096dc69dc408bf7d07fcd39a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "e79db1a382e61436ed81f9f47dc06d7a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ua.png", "hash": "dbd97cfa852ffc84bfdf98bc2a2c3789"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_method_call.dart", "hash": "da6f500c03c005a207d38c1daf24b00a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib/src/bloc_observer.dart", "hash": "24186b71ba0dedb4088cfaa009dcc695"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_linux-0.0.5/lib/parse_args.dart", "hash": "3bb047b02b25a0fbfbe268d52be61749"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "9bf11cc1ea784a251bf67350f02f910f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bj.png", "hash": "9b503fbf4131f93fbe7b574b8c74357e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/predicate.dart", "hash": "4fcb0c3d6a9c166d16c124c91e33dcb6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/character.dart", "hash": "f6f8ad33193db66deb89d68e406eeaf9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/aabb2.dart", "hash": "8a05c4ee4d75a485389f2e5c2f6618e6"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/s3.svg", "hash": "989bc011c6214a45ac1e58a57d98e2cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/axis/numeric_axis.dart", "hash": "80cf4a81b7890c233b741ef32bab0d6d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/tag_exception.dart", "hash": "d851ccbe29621b2c3cf5556211b35b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispvoice.dart", "hash": "ace74499f232b87549db3ce1828579ca"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/nudronlogo.png", "hash": "2f1fdb08569aeb7f67661a9564a8850c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/sa.png", "hash": "ef836bd02f745af03aa0d01003942d44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/in_app_update-4.2.3/lib/in_app_update.dart", "hash": "10dbcae7516fba6b41aa1bdb57a6e130"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/pl_messages.dart", "hash": "426788530891537f946ce9a3a9913527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/immdeviceenumerator.dart", "hash": "f31bb216ea8990a64c2326c16fd2ea33"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gb-nir.png", "hash": "fc5305efe4f16b63fb507606789cc916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/messages.g.dart", "hash": "1567572a579e5f2aab31966d4a056855"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/tool_panel/tool_panel.dart", "hash": "32c026fa29d636a85ae5829371fd9458"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/lib/url_launcher_android.dart", "hash": "42d0000dd58d923eb70183595232c299"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/constant.dart", "hash": "7f01c223a9584977891a4a70396541d0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/kr.png", "hash": "9e2a9c7ae07cf8977e8f01200ee2912e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/us.png", "hash": "b1cb710eb57a54bc3eea8e4fba79b2c1"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/s1.svg", "hash": "e1f3e78d6dd2ff8449f5669a298bee19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/date_symbols.dart", "hash": "4c94c1ae460dd53255786f0ce3b53463"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "7f2ccd6eece375fce2e247d3995e45c5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/rs.png", "hash": "d3e3217aabf6387166320514841c14ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_linux-0.0.5/lib/open_file_linux.dart", "hash": "c2c00b16be7d258a1d4703437c3da039"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/tc.png", "hash": "6f2d1a2b9f887be4b3568169e297a506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/sony_xperia_1_ii/frame.g.dart", "hash": "8cb3cfc703200d872123cd7ce3b4efb5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/global_state.dart", "hash": "dc4e3bf96e9c6e94879d54eaa2f81c69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/fast_line_series.dart", "hash": "e03a8029459078edd5409748b7ae58d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.46/lib/src/auth_messages_android.dart", "hash": "cd9db4ac4b35f0a15d74d1c6ce32a107"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "d9a659644f1b667686f2c9b22545dc0e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ag.png", "hash": "9bae91983418f15d9b8ffda5ba340c4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_n.dart", "hash": "8fe95cebce3f522e41f0bef51a1818b7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "hash": "6b12064a1fbfe893f4a13d8f14d70a9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.3/lib/src/messages.g.dart", "hash": "bee9a89328e73d06f9b915e157deffe1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "3ad691d7f4e0dfc9bac177f56b288925"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ht.png", "hash": "009d5c3627c89310bd25522b636b09bf"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bh.png", "hash": "6e48934b768705ca98a7d1e56422dc83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudiorenderclient.dart", "hash": "678125b16711755ee7950f73890a3360"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bm.png", "hash": "eb2492b804c9028f3822cdb1f83a48e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/fi_messages.dart", "hash": "93c2f2419d5e20de88f9950cd4555354"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/re.png", "hash": "6cd39fe5669a38f6e33bffc7b697bab2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/lc.png", "hash": "055c35de209c63b67707c5297ac5079a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/ram_file_handle.dart", "hash": "56cd7d8ad7922478cc5edefe424c1401"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/utils.dart", "hash": "e85b4f3cf370581b3ef11497a9a5bce3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/Users/<USER>/Development/flutter/bin/internal/engine.version", "hash": "32c4a9b6e6aa250ffd4aba05be285558"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/sk.png", "hash": "0f8da623c8f140ac2b5a61234dd3e7cd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "6cae6900e82c94905cc2aaefd806f8eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/tr_messages.dart", "hash": "a97587b4a082706d52af8193a350855a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/view_model/loginPostRequests.dart", "hash": "8b94cb15a637fbd0aa10676305219ae5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gb-wls.png", "hash": "72005cb7be41ac749368a50a9d9f29ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.2.3/lib/src/service_status.dart", "hash": "ea191ed02800df2436f2ba4d1443acd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/hexagon_dots/hexagon_dots.dart", "hash": "64b111353014b52072b2be6a70b25971"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "1e840a2c03797a7468018e124b957d2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/connector_line.dart", "hash": "881706dd6ad71f2edea11792b7acc11d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.17/lib/src/utilities/compute.dart", "hash": "75825be8545647641c245afbba6962e3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "6987c3474a94dd1c4ff8f8540212f16b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/hamburger.svg", "hash": "8d738d797a4e5a2a1a113c53cf2cb3d3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/kp.png", "hash": "fd6e44b3fe460988afbfd0cb456282b2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/tc.png", "hash": "6f2d1a2b9f887be4b3568169e297a506"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mc.png", "hash": "412ce0b1f821e3912e83ae356b30052e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cz.png", "hash": "482c8ba16ff3d81eeef60650db3802e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/device_info_plus_windows.dart", "hash": "9649ae3abaa2347c68833b467110ccf5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/predicate.dart", "hash": "9d95e55b0ed6080e677989c4e1e1cff6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/de_messages.dart", "hash": "c032cb36b7900c73ffd764ab88e9675c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/shared_preferences_foundation.dart", "hash": "b72ebe27944e3a75601e56579bb92907"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "e5b4b18b359c9703926f723a1b8dd4ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sms_autofill-2.4.1/lib/sms_autofill.dart", "hash": "fd5a89d1a48bcb3c6826ad425d9364bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/windows_device_info.dart", "hash": "93faa63229d86e5b66c6fa4fd0554f1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/attribute_type.dart", "hash": "a9d570114e5a6e733fb029f6b3cffad7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mm.png", "hash": "b664dc1c591c3bf34ad4fd223922a439"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bm.png", "hash": "eb2492b804c9028f3822cdb1f83a48e2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mk.png", "hash": "8b17ec36efa149749b8d3fd59f55974b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/sibling.dart", "hash": "6cee72f673d593b0b84628bf243727a8"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "be0a77cf3f0463f3dacd09ec596d9002"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/clock.dart", "hash": "2c91507ecca892cf65c6eaf3fbe0a7e6"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "458f3bf784829a083098291a97123e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/isupporterrorinfo.dart", "hash": "0318359df96d8b438340156129fd1c68"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gy.png", "hash": "75f8dd61ddedb3cf595075e64fc80432"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter_localizations/lib/src/l10n/generated_date_localizations.dart", "hash": "26bb5716eba58cdf5fb932ac3becd341"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gh.png", "hash": "c73432df8a63fb674f93e8424eae545f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "45beeaf92542183f39c458a87dcc81f7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/dashboards/TrendsTable.dart", "hash": "84726301222705605b0e73c3a8991492"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "85cf42bafb7c0646bd7a99379649da29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_platform_interface-1.0.3/lib/src/platform_interface/open_file_platform.dart", "hash": "fc8e6942d75d9de01f9ee5d60efc20b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/lt.png", "hash": "e38382f3f7cb60cdccbf381cea594d2d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "b29e302994b1b0ea5029734406101b8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/intersection_result.dart", "hash": "832666b4f69945b957b6399ec677085b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.4/lib/src/strings.dart", "hash": "4e96c754178f24bd4f6b2c16e77b3a21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispellchecker.dart", "hash": "b868a7ab9e1be413c489dc9958bf907b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_12/frame.g.dart", "hash": "1cbb139cab8b8434bfe0ee12feddcc94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/micro_money.dart", "hash": "391b7eda9bffdd4386292eae157d449c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/dk.png", "hash": "f9d6bcded318f5910b8bc49962730afa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/nc.png", "hash": "a3ee8fc05db66f7ce64bce533441da7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/localizations.dart", "hash": "bf1918c6db450b76141f2f952babc8b6"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "44d59e37041b6305018f70012fef7d52"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ls.png", "hash": "62d8217df17e09322c227ee8683d8dd3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "db6f70d83d36597cc6bc3eaaffd10aaa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/reflection/iterable.dart", "hash": "a75fb12af280fb36882c184800a13fde"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_bus_name.dart", "hash": "9cf807e15d1e83af4f62cdeb36582a91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ienumspellingerror.dart", "hash": "c2b3370ba518e83a18e0be246f0e2ed4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/config.dart", "hash": "f313a026a7ae0594170e7c7496ddb55c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "39f5f34a4d3615c180c9de1bf4e8dde8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/tg.png", "hash": "82dabd3a1a4900ae4866a4da65f373e5"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "e6f282a4b33b70c7d1d06bec39b155f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/tk_messages.dart", "hash": "d1053c5ba3ec6557e30ec634c0378181"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/getsid.dart", "hash": "5261078afe15bcdc637478bb6d7f7e21"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/kw.png", "hash": "b2afbb748e0b7c0b0c22f53e11e7dd55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/error_bar_series.dart", "hash": "f5bf5080de4a1e3e3717595a1a32dad7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/url_launcher.dart", "hash": "10bbfa83fe7c3c8f8a4964a3e96e5b58"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "68eb8647107febe1419211e153b27a54"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "cd7f8dc942f5138db121aabbaba920ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/dxva2.g.dart", "hash": "73ec60b4a67001fb2adfab990c932c6e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/a1.svg", "hash": "ab1a7a98dfd129b91c50c89d7684558b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/small.dart", "hash": "688b7a00bbc8cd374ccfcbc2ea15d1b8"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gi.png", "hash": "58894db0e25e9214ec2271d96d4d1623"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/type_exception.dart", "hash": "abf77351ef7991f21d4f50727b72d4ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/extensions/set_ansi.dart", "hash": "d30eba29d046c1a8b7f029838de6e49f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_family_with_variant.dart", "hash": "1562c4a8bfee3d68c041674517ef436c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_client.dart", "hash": "3af46f5bcbf66358a84fbed56846af4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/save/save_file.dart", "hash": "6b9b1e8219ab6ea744738d81b7c8efbd"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/hm.png", "hash": "600835121397ea512cea1f3204278329"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/tk.png", "hash": "87e390b384b39af41afd489e42b03e07"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/cn.png", "hash": "6b8c353044ef5e29631279e0afc1a8c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/opengl.dart", "hash": "21baec3598b81f16065716b8ee97c8bb"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "74902317f9caa3ba9c05b114d45d8a02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector3.dart", "hash": "a1e740a70209acedc9ba1bff7141c14c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/box_and_whisker_series.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/structs.g.dart", "hash": "67f751cf689639227d5db57f73b92a2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/src/dbus_server.dart", "hash": "d267d14ea9e30641995ebfa6d32e6f4f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/km.png", "hash": "204a44c4c89449415168f8f77c4c0d31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationdragpattern.dart", "hash": "2d186bf86fb26df1aca63c78d1f3da0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ss.png", "hash": "842472f6dfe9dc2e01d7efd4a9877d41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/lib/src/pin_code_fields.dart", "hash": "d3485563fc023fb3238d34493809bc5d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "1fd7c932679011d491315ff136d13822"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/pn.png", "hash": "ffa91e8a1df1eac6b36d737aa76d701b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "b3686e0781f3148d75a64ebb2bfef609"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/views/tool_panel/sections/system.dart", "hash": "d49f5609a6a121269c8932ede374dc1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_breadcrumb-1.0.1/lib/flutter_breadcrumb.dart", "hash": "12bebba5a5c6a3c4d6665367ce1beb83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.0.13/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/chart_series.dart", "hash": "f0bd33fdc523f50744abf68090459bdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/utils.dart", "hash": "52542f6a97f7fdd32cbcbfef08274a9c"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/images/basic_advance.png", "hash": "0f600be57e17529f4887fc79100a4ef8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib/vector_graphics_codec.dart", "hash": "1cb908a37b3d1b606ce345ce629694b9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "0575a78fbb39a292302737868752da77"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "3d892f04e5e34b591f8afa5dcbcee96d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/km_messages.dart", "hash": "28a4816855c345f70c0378c187a950ee"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/my.png", "hash": "7b4bc8cdef4f7b237791c01f5e7874f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/api_ms_win_core_comm_l1_1_1.g.dart", "hash": "9c3c2afae62dafae40a282af7f685943"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gf.png", "hash": "71678ea3b4a8eeabd1e64a60eece4256"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_web-0.0.4/LICENSE", "hash": "54ca5f4345f518f70f047f1f2e585945"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "c8260e372a7e6f788963210c83c55256"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "5af6304445e6664f6caca9ed4b5e885f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ipersistmemory.dart", "hash": "06bcab18a6206389adfe991144246ffc"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bi.png", "hash": "fb60b979ef7d78391bb32896af8b7021"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad_pro_12Inches_gen4/device.dart", "hash": "7f4d6ef9585e264dca38c0290bf49a1b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "044d6bef26a97ada1d56ff6fe9b7cc14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestreader7.dart", "hash": "f697b51a3a96ab52efa2c082f20a738a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ch.png", "hash": "7690a93c44b5212b11c7e49b218fb2cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "e5a3ca065f292c0f0b0cca0a55df41aa"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/my.png", "hash": "7b4bc8cdef4f7b237791c01f5e7874f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/ole32.g.dart", "hash": "c1527bbe7fe6973a697108d13c3da85b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/pk.png", "hash": "0228ceefa355b34e8ec3be8bfd1ddb42"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "a5d0509a39803ffb48cae2803cd4f4bd"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/zw.png", "hash": "d5c4fe9318ebc1a68e3445617215195f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/src/streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/interactions/behavior.dart", "hash": "ffa6ae72f7816fe0a8fdf71e567c3db9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "107c33a245427bf0f05e21c250653dc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/core_legend.dart", "hash": "a5a317bc2456821a18e80cd560c95a45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_12/device.dart", "hash": "55b2560659f48a0bd7c727894d0fcade"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_location.dart", "hash": "f91bd03132e9e671e87f0b9066647164"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "f20071b459b9bbb98083efedeaf02777"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iaudiosessionmanager.dart", "hash": "487d0d91f9dc55efcbc2a686bbf46b8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/immendpoint.dart", "hash": "08f987c2f95b3e2a51c435bd8e8c588f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/types.dart", "hash": "83bb9dfd0d336db35e2f8d73c2bdda85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/sheet/data_model.dart", "hash": "29848fbbbad94130275ec8128aa2d1a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/nested.dart", "hash": "5c621d343831cbb9619557942e6b7d9f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/a7.svg", "hash": "d6a14672566f812c3bf72589ac471a7d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/fo.png", "hash": "0bfc387f2eb3d9b85225d61b515ee8fc"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "826b67d0d6c27e72e7b0f702d02afcec"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/an.png", "hash": "469f91bffae95b6ad7c299ac800ee19d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.15/lib/vector_graphics.dart", "hash": "671b26ea03821b4c63c0fe2fd64f9e87"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "a2f376b739fa28d7a71312ecf31d6465"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/utils/typedef.dart", "hash": "c9aceda2a936c7032167f77745e65acd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "6b48e1348ae677efad30c0a9d4600e38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_u.dart", "hash": "de4ba796e7c200bdc07306e8b82e1f5a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "2610f7ca2c31b37ad050671aafbccdd9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "31db92b0b980a193d02b613bb9c0f819"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/half_triangle_dot/half_triangle_dot.dart", "hash": "f8bbb6766ec221ff0555d0d848bda400"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "04c960ae6d770135bb0b6acf14b134a4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "5d8e29422039d9dcce6908b427814d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_w.dart", "hash": "36e5b08967f3abd15930bde25e9d2ccb"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/pwd.svg", "hash": "166e2b3a0f1294339990a9efbe3599be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.15/lib/src/html_render_vector_graphics.dart", "hash": "2ac99a26ca075c8cd9f8f7ffb741f3ad"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "00dfe436d7f3546993ad86cc4f9ff655"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/url_launcher_linux.dart", "hash": "9d67bda83980287cc1100fe7fad9e05d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/api_ms_win_wsl_api_l1_1_0.g.dart", "hash": "6c31b298eba9c0df399049d9072d5ede"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "be66f00d2c9bb816f4236dd0f92bff55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/types.dart", "hash": "102fd2fe72902d82633e70e32ec6ea7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/macos/macbook_pro/frame.dart", "hash": "25610a9104bdbc1add5afda6e7187a30"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/bl.png", "hash": "30f55fe505cb4f3ddc09a890d4073ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/src/messages.g.dart", "hash": "114597dbbcfb24754b14f8261211d90f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "ced9d2439e23015bfc2bac438f598985"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/drawers/profile/2fadisabled.dart", "hash": "01a9819f3a9e6c76bff527fe2574375f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/pt_br_messages.dart", "hash": "e08f3255461a95f8c0f48435658a8163"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/indicators/atr_indicator.dart", "hash": "fc5e230607b377f66d8a85f75583aebf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/magnification.g.dart", "hash": "e950e207ecdcf1d767721554751c6673"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/table/fields/IconHeader.dart", "hash": "7222c76da03044984128d9d96236c611"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/by.png", "hash": "caec57cd103f0c35d90946540e69e3de"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "ffa4f7b2d5b1caccc05cf4b64021ae5e"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/images/2falogo.svg", "hash": "ba696a5958a18c4f033087f43ee41a0b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "12143f732513790cd579481704256dcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/axis/category_axis.dart", "hash": "8491d602eb6b394c9f85fb973849ade4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/gb-sct.png", "hash": "075bb357733327ec4115ab5cbba792ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/sv_messages.dart", "hash": "d2fb492f89c6314f7d8e08820e2c098c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxfilesenumerator.dart", "hash": "ffc5c2e273fa5a533521f5e67f6e183f"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/so.png", "hash": "cfe6bb95bcd259a3cc41a09ee7ca568b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "c97a8ffd51479d05a18a54ac27ccba15"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ee.png", "hash": "54aa1816507276a17070f395a4a89e2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/preceding.dart", "hash": "9d5375413b37f738384990ebdd6c6285"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/drag_boundary.dart", "hash": "1e0ea989110b1544dbaf1fdf3d9864cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/link.dart", "hash": "c36f00a660d9aa87ebeab8672ccc6b32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.4/lib/src/messages.g.dart", "hash": "c528947be6781565b850f7e1c087b753"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/archive_file.dart", "hash": "23b52b19a3120b5d10380eac9dbede3b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib/src/transition.dart", "hash": "0998ddc4569366ddc74cb9d6069369c3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "7abc7e5212374d29bfe5372de563f53c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/default.dart", "hash": "7f30d05e05b047b274b1c4b45391d698"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtextpattern.dart", "hash": "6e8a57cfea32b9c9f29b229edeacbd6b"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/nfcicon.svg", "hash": "fb3685554026dbe621d551b0700157bc"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/no.png", "hash": "f7f33a43528edcdbbe5f669b538bee2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/greedy.dart", "hash": "ea92027aa0f1a05330a4a0cfdfa5033f"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "7bdfcadf7dd131e95092d30909e5b11f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/frame.dart", "hash": "ac1ac3a59b21153bc851e922d554d809"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ae.png", "hash": "045eddd7da0ef9fb3a7593d7d2262659"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "8807672a31b470f53c5fcc2b36dcf509"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bn.png", "hash": "94d863533155418d07a607b52ca1b701"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "ff2b2e7159e19374f968cf529da25c01"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "82604e7dbb83dc8f66f5ec9d0962378b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/widgets/rounded_rectangle.dart", "hash": "ea320039b5dfae31e7b4ccb6516d3119"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_13_pro_max/frame.g.dart", "hash": "a57d188161069b84f0d20367299aea72"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "2936a409e1029ec52f7c0003f4db18c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.15/lib/src/debug.dart", "hash": "d3b50e217be5e58d53f746ba267e30e9"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "484329e20b76c279413a7d6dc78b3223"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "577ec098e9f6651d7704fad48b4dd44a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/mc.png", "hash": "412ce0b1f821e3912e83ae356b30052e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "0d1b13fd16692571d5725f164d0964ef"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/is.png", "hash": "22358dadd1d5fc4f11fcb3c41d453ec0"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "c679063104d2f24639459c8ab3eed77a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/clipping_optimizer.dart", "hash": "c77ae533b618bb24965504354af17bb3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "1ed34d373b037c1696e90bf7e4f249ba"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/containers/exportToExcel.dart", "hash": "e18c49052ab5445a7beff70ff29747c8"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/za.png", "hash": "aa749828e6cf1a3393e0d5c9ab088af0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/sheet/sheet.dart", "hash": "edc0a7a873366bc9025371300ed2d67a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/waterfall_series.dart", "hash": "70ddc4b8712a205dfcb07e8b5d60ec45"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "aef544fef0ced7679e0edaf5f8d036b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_o.dart", "hash": "d14d602c73240e571385abe6192469f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/crc32.dart", "hash": "52ebee94bf958053713e187f7904dca4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ky.png", "hash": "666d01aa03ecdf6b96202cdf6b08b732"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/s2.svg", "hash": "e83f818a47b01b8667cbba2f5a0996d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ls.png", "hash": "62d8217df17e09322c227ee8683d8dd3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "eabd3dc33b1a3a2966fa68f6efeb6bce"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/il.png", "hash": "b72b572cc199bf03eba1c008cd00d3cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/lib/messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/src/state/custom_device.dart", "hash": "1d71cc37e29d46e31dedac375b14e69a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/LICENSE", "hash": "f1f878760e25d026978348f63130742a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/LICENSE", "hash": "9741c346eef56131163e13b9db1241b3"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "85e90b0b1f705d7db10d294017bcaf44"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "7e0e723348daf7abfd74287e07b76dd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterator.dart", "hash": "4d16182e94ac3ec4a2804eb97efe7842"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "266a40131c9f05494e82934fd7096ed0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ye.png", "hash": "1d5dcbcbbc8de944c3db228f0c089569"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter_localizations/lib/src/l10n/generated_cupertino_localizations.dart", "hash": "5ea2fdad8a965d5851e2d435f1b0c7b6"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/images/error.png", "hash": "d5ee96ce1fff0eca4f1f4cb22f9fa112"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "2553e163ea84c7207282c18b5d9e14c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/widgets/draw_arc.dart", "hash": "ba4678324fe76e174b2fcc0f78223180"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/aes.dart", "hash": "5bbe37094357ce641996c2550b14c417"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/um.png", "hash": "b1cb710eb57a54bc3eea8e4fba79b2c1"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "a55ac84003178cdc783ca41a634500a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_12_pro_max/frame.g.dart", "hash": "617d5fa703ce33a37d263b02299f0692"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "8e58a1e955460cf5a4ea1cea2b7606cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/numbers.dart", "hash": "89c939c52ff938d3e903e0e67563d980"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/android/devices.dart", "hash": "9b655d8a827ae60cb14b2de6db31ca67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/common/chart_point.dart", "hash": "b7d22aafffeaa508a542980bb53d86ea"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter_localizations/lib/src/material_localizations.dart", "hash": "1f02785d9578dfad29a08ad8f41b02af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/pie_series.dart", "hash": "59b353fbf6883fdf4a266596160e613d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "ee62fb3be5d885d65054fac4b84cac6c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "82ea4f7076bd7e32c383a2466518b943"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/src/twisting_dots/twisting_dots.dart", "hash": "b57c15d2c1fe7cd176399c8232547947"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "61137458bbcab0dfb643d5d50a5ae80f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/win32/uxtheme.g.dart", "hash": "30d51f71b24984c4980f3f3c13df8190"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "dbbc7f46620d816e615bbbe67eb258e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.4/lib/src/shared_preferences_async_android.dart", "hash": "5cfe2d9d61584eae2e9c8e81be1dd9c5"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/app_icon.png", "hash": "66a247274637120847556fbf1d6ae725"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/is.png", "hash": "22358dadd1d5fc4f11fcb3c41d453ec0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ishelllink.dart", "hash": "8b90b8fa4eae6234d9cdad3987f9faf3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.6.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/aabb3.dart", "hash": "4d9f681599b9aba645421097eda46139"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/definition/reference.dart", "hash": "3881ad72fbb323a843aa4bf47c99422d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad_air_4/screen.g.dart", "hash": "7ded361a0e476b681cde0e3187a4cebc"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/pt.png", "hash": "b4cf39fbafb4930dec94f416e71fc232"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/gzip_encoder.dart", "hash": "0ab8a127ada2857bf6593f8daa6ea174"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/date_builder.dart", "hash": "bc1f35bad7b3fd785bd8734292b27ff7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/asset_manifest.dart", "hash": "604151cdbd54ee0f0f4681fc8840d827"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "956c84257f1efe6f10ab24f3d6702307"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/a6.svg", "hash": "8e82073ea9d0d268ce1427fb1281d818"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/ios_device_info.dart", "hash": "2ac6c71ca1b3241eec36c619a2db2c4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screenshot-3.0.0/lib/src/platform_specific/file_manager/file_manager_io.dart", "hash": "2574d80f383e9137c63dbf4fa2f91636"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_b.dart", "hash": "bf5efe9b7f7e8bdc46aa542818534985"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/LICENSE", "hash": "d81624acce4538cc3c6485dbba176499"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/colors.dart", "hash": "a385ed3a073e36a430c51f9641564853"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/kp.png", "hash": "fd6e44b3fe460988afbfd0cb456282b2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "fb23ec509c4792802accd10fa7c8a6b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/continuation.dart", "hash": "68166eac4341d04e2ade4cf3462ae722"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/assets/icons/s3.svg", "hash": "989bc011c6214a45ac1e58a57d98e2cd"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "34ebb85f7f2122d2e1265626cf252781"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/aq.png", "hash": "c57c903b39fe5e2ba1e01bc3d330296c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/src/utils.dart", "hash": "ce30848ef1f94b243d6094ee0d740597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/src/number_format/num_format.dart", "hash": "4c9633f5e7a9f2f8b93652c13d20ce4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/ispeechaudioformat.dart", "hash": "36145af4fe8f10df91f98b13659a7b23"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/cz.png", "hash": "482c8ba16ff3d81eeef60650db3802e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/util/output_stream.dart", "hash": "3ca8f03581035097f3569def9d3fbd7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/registry_value.dart", "hash": "3a76b28bc60cde234df7a417b1aa2ddb"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "caad40ad1936874ea93473b300bb909c"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "c7d65c476f653e952aedcb0cbcab3c73"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/io.png", "hash": "8021829259b5030e95f45902d30f137c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationlegacyiaccessiblepattern.dart", "hash": "147fdd9503161f6606b625f0ed5c1272"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/allowed_keys_helpers.dart", "hash": "41696c18e708950dccaf7cb4f5b7b195"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/pr.png", "hash": "ac1c4bcef3da2034e1668ab1e95ae82d"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "feacc941aea1ec8b3a30601915b7d353"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/constants_metadata.dart", "hash": "4d74fe38db99d0b2b135f8d4f81d6721"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/text.dart", "hash": "f52860ffbd4c6858f092292d1589d556"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "8dedd49e916a59b6940a666481d82e10"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "2906cf9308cbed8eb54ab1638dd5f56e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/fi.png", "hash": "a79f2dbc126dac46e4396fcc80942a82"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gi.png", "hash": "58894db0e25e9214ec2271d96d4d1623"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/lzma/range_decoder.dart", "hash": "72396af1725b7733f0558db5fa09e283"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/series/stepline_series.dart", "hash": "8ae6ab827362d92a74debfc4739c396b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad_pro_12Inches_gen2/device.dart", "hash": "8d03d10cb9771e8c3adbf723f9bbe822"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/th.png", "hash": "d4bd67d33ed4ac74b4e9b75d853dae02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.17/lib/src/cache.dart", "hash": "6e8886f8cef9ae40f50a953811fd6376"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad_pro_12Inches_gen4/frame.g.dart", "hash": "6db10bb84b397451b371e12347a28d09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/src/zlib/huffman_table.dart", "hash": "61d730712ef42fa468853460c529f0a2"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "2cb8483d7aa2b998d4641e25a0425f67"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "ad631d7cd122efc4862c1c084fbde716"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/rw_messages.dart", "hash": "e05ff10ffecaf4b5a62187144eb03ffe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/nz.png", "hash": "b48a5e047a5868e59c2abcbd8387082d"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ki.png", "hash": "69a7d5a8f6f622e6d2243f3f04d1d4c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/ipad_pro_11inches/screen.g.dart", "hash": "fad4466596e8c818389d12e038354888"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/views/widgets/containers/CustomAppBar.dart", "hash": "1851111e30ee4ffcf524b327912a462b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iappxmanifestpackagedependency.dart", "hash": "793424ed524885eedef0340c067b865e"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "160e007517eb9af8299b242a217c6ff9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/provider.dart", "hash": "08fb5f27432143c416f473db763fa8c6"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "3fa7a3bafbab98c305119475eb004a06"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "d942bc7ece253c7918e1f60d35e233b0"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gb.png", "hash": "fc5305efe4f16b63fb507606789cc916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationtextpattern2.dart", "hash": "7c3e512b5c20c07ddded2fb71eadd848"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/gs.png", "hash": "524d0f00ee874af0cdf3c00f49fa17ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_d.dart", "hash": "2a101a9f7dc3955fa1a1cb93fde33565"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/ao.png", "hash": "d19240c02a02e59c3c1ec0959f877f2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/src/com/iuiautomationgridpattern.dart", "hash": "142eee94af4418beb50a22e4c3970309"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/color_mapper.dart", "hash": "5baf64b18f36d2e7620e01237c625a19"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/ax.png", "hash": "ffffd1de8a677dc02a47eb8f0e98d9ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/element.dart", "hash": "23db80d93d6f37b73648e830d1dda0f4"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/tn.png", "hash": "87f591537e0a5f01bb10fe941798d4e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.17/lib/src/utilities/file.dart", "hash": "cf3d93bae83850abf2c5e943a6b1ccbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/utils/labeled.dart", "hash": "715bccb8e9ba9889573a60bf0e457402"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/ta_messages.dart", "hash": "7df089819cb9d042e16cfe58f6401ded"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/mq.png", "hash": "446edd9300307eda562e5c9ac307d7f2"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/app/intermediates/flutter/debug/flutter_assets/packages/country_code_picker/flags/bo.png", "hash": "92c247480f38f66397df4eb1f8ff0a68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/src/messages/hi_messages.dart", "hash": "8d4e0d6959f589228c8861db63be887f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/src/devices/ios/iphone_13_mini/screen.g.dart", "hash": "0d7c5c5989319571572319f9fd84340c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.15/lib/src/render_vector_graphic.dart", "hash": "3f4bc5c1ecc7c8c1756db79f8be177b4"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "12a21ff35182c138908274c8b66714d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/src/charts/pyramid_chart.dart", "hash": "18cb3ee948e51d4d6046d53dbd51a123"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/flags/tw.png", "hash": "88d58bc88a85dd550ddec586274e582e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/src/network_manager_client.dart", "hash": "60838abe37c945cf06c1b5ccc5066fed"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "05d4aeae6031730c6aa412a128f67448"}, {"path": "/Users/<USER>/Development/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "b3019bcd49ebc4edd28b985af11a4292"}]}